"""
Do NOT manually edit this file.
Instead, run ./bin/ask_update.py.
"""

defined_facts = [
    'algebraic',
    'antihermitian',
    'commutative',
    'complex',
    'composite',
    'even',
    'extended_negative',
    'extended_nonnegative',
    'extended_nonpositive',
    'extended_nonzero',
    'extended_positive',
    'extended_real',
    'finite',
    'hermitian',
    'imaginary',
    'infinite',
    'integer',
    'irrational',
    'negative',
    'noninteger',
    'nonnegative',
    'nonpositive',
    'nonzero',
    'odd',
    'positive',
    'prime',
    'rational',
    'real',
    'transcendental',
    'zero',
] # defined_facts


full_implications = dict( [
    # Implications of algebraic = True:
    (('algebraic', True), set( (
        ('commutative', True),
        ('complex', True),
        ('finite', True),
        ('infinite', False),
        ('transcendental', False),
       ) ),
     ),
    # Implications of algebraic = False:
    (('algebraic', False), set( (
        ('composite', False),
        ('even', False),
        ('integer', False),
        ('odd', False),
        ('prime', False),
        ('rational', False),
        ('zero', False),
       ) ),
     ),
    # Implications of antihermitian = True:
    (('antihermitian', True), set( (
       ) ),
     ),
    # Implications of antihermitian = False:
    (('antihermitian', False), set( (
        ('imaginary', False),
       ) ),
     ),
    # Implications of commutative = True:
    (('commutative', True), set( (
       ) ),
     ),
    # Implications of commutative = False:
    (('commutative', False), set( (
        ('algebraic', False),
        ('complex', False),
        ('composite', False),
        ('even', False),
        ('extended_negative', False),
        ('extended_nonnegative', False),
        ('extended_nonpositive', False),
        ('extended_nonzero', False),
        ('extended_positive', False),
        ('extended_real', False),
        ('imaginary', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of complex = True:
    (('complex', True), set( (
        ('commutative', True),
        ('finite', True),
        ('infinite', False),
       ) ),
     ),
    # Implications of complex = False:
    (('complex', False), set( (
        ('algebraic', False),
        ('composite', False),
        ('even', False),
        ('imaginary', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of composite = True:
    (('composite', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_nonpositive', False),
        ('extended_nonzero', True),
        ('extended_positive', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', True),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', True),
        ('nonpositive', False),
        ('nonzero', True),
        ('positive', True),
        ('prime', False),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of composite = False:
    (('composite', False), set( (
       ) ),
     ),
    # Implications of even = True:
    (('even', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', True),
        ('irrational', False),
        ('noninteger', False),
        ('odd', False),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
       ) ),
     ),
    # Implications of even = False:
    (('even', False), set( (
        ('zero', False),
       ) ),
     ),
    # Implications of extended_negative = True:
    (('extended_negative', True), set( (
        ('commutative', True),
        ('composite', False),
        ('extended_nonnegative', False),
        ('extended_nonpositive', True),
        ('extended_nonzero', True),
        ('extended_positive', False),
        ('extended_real', True),
        ('imaginary', False),
        ('nonnegative', False),
        ('positive', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of extended_negative = False:
    (('extended_negative', False), set( (
        ('negative', False),
       ) ),
     ),
    # Implications of extended_nonnegative = True:
    (('extended_nonnegative', True), set( (
        ('commutative', True),
        ('extended_negative', False),
        ('extended_real', True),
        ('imaginary', False),
        ('negative', False),
       ) ),
     ),
    # Implications of extended_nonnegative = False:
    (('extended_nonnegative', False), set( (
        ('composite', False),
        ('extended_positive', False),
        ('nonnegative', False),
        ('positive', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of extended_nonpositive = True:
    (('extended_nonpositive', True), set( (
        ('commutative', True),
        ('composite', False),
        ('extended_positive', False),
        ('extended_real', True),
        ('imaginary', False),
        ('positive', False),
        ('prime', False),
       ) ),
     ),
    # Implications of extended_nonpositive = False:
    (('extended_nonpositive', False), set( (
        ('extended_negative', False),
        ('negative', False),
        ('nonpositive', False),
        ('zero', False),
       ) ),
     ),
    # Implications of extended_nonzero = True:
    (('extended_nonzero', True), set( (
        ('commutative', True),
        ('extended_real', True),
        ('imaginary', False),
        ('zero', False),
       ) ),
     ),
    # Implications of extended_nonzero = False:
    (('extended_nonzero', False), set( (
        ('composite', False),
        ('extended_negative', False),
        ('extended_positive', False),
        ('negative', False),
        ('nonzero', False),
        ('positive', False),
        ('prime', False),
       ) ),
     ),
    # Implications of extended_positive = True:
    (('extended_positive', True), set( (
        ('commutative', True),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_nonpositive', False),
        ('extended_nonzero', True),
        ('extended_real', True),
        ('imaginary', False),
        ('negative', False),
        ('nonpositive', False),
        ('zero', False),
       ) ),
     ),
    # Implications of extended_positive = False:
    (('extended_positive', False), set( (
        ('composite', False),
        ('positive', False),
        ('prime', False),
       ) ),
     ),
    # Implications of extended_real = True:
    (('extended_real', True), set( (
        ('commutative', True),
        ('imaginary', False),
       ) ),
     ),
    # Implications of extended_real = False:
    (('extended_real', False), set( (
        ('composite', False),
        ('even', False),
        ('extended_negative', False),
        ('extended_nonnegative', False),
        ('extended_nonpositive', False),
        ('extended_nonzero', False),
        ('extended_positive', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('zero', False),
       ) ),
     ),
    # Implications of finite = True:
    (('finite', True), set( (
        ('infinite', False),
       ) ),
     ),
    # Implications of finite = False:
    (('finite', False), set( (
        ('algebraic', False),
        ('complex', False),
        ('composite', False),
        ('even', False),
        ('imaginary', False),
        ('infinite', True),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of hermitian = True:
    (('hermitian', True), set( (
       ) ),
     ),
    # Implications of hermitian = False:
    (('hermitian', False), set( (
        ('composite', False),
        ('even', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('zero', False),
       ) ),
     ),
    # Implications of imaginary = True:
    (('imaginary', True), set( (
        ('antihermitian', True),
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('even', False),
        ('extended_negative', False),
        ('extended_nonnegative', False),
        ('extended_nonpositive', False),
        ('extended_nonzero', False),
        ('extended_positive', False),
        ('extended_real', False),
        ('finite', True),
        ('infinite', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('zero', False),
       ) ),
     ),
    # Implications of imaginary = False:
    (('imaginary', False), set( (
       ) ),
     ),
    # Implications of infinite = True:
    (('infinite', True), set( (
        ('algebraic', False),
        ('complex', False),
        ('composite', False),
        ('even', False),
        ('finite', False),
        ('imaginary', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('real', False),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of infinite = False:
    (('infinite', False), set( (
        ('finite', True),
       ) ),
     ),
    # Implications of integer = True:
    (('integer', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('irrational', False),
        ('noninteger', False),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
       ) ),
     ),
    # Implications of integer = False:
    (('integer', False), set( (
        ('composite', False),
        ('even', False),
        ('odd', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of irrational = True:
    (('irrational', True), set( (
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('even', False),
        ('extended_nonzero', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', False),
        ('noninteger', True),
        ('nonzero', True),
        ('odd', False),
        ('prime', False),
        ('rational', False),
        ('real', True),
        ('zero', False),
       ) ),
     ),
    # Implications of irrational = False:
    (('irrational', False), set( (
       ) ),
     ),
    # Implications of negative = True:
    (('negative', True), set( (
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('extended_negative', True),
        ('extended_nonnegative', False),
        ('extended_nonpositive', True),
        ('extended_nonzero', True),
        ('extended_positive', False),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('nonnegative', False),
        ('nonpositive', True),
        ('nonzero', True),
        ('positive', False),
        ('prime', False),
        ('real', True),
        ('zero', False),
       ) ),
     ),
    # Implications of negative = False:
    (('negative', False), set( (
       ) ),
     ),
    # Implications of noninteger = True:
    (('noninteger', True), set( (
        ('commutative', True),
        ('composite', False),
        ('even', False),
        ('extended_nonzero', True),
        ('extended_real', True),
        ('imaginary', False),
        ('integer', False),
        ('odd', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of noninteger = False:
    (('noninteger', False), set( (
       ) ),
     ),
    # Implications of nonnegative = True:
    (('nonnegative', True), set( (
        ('commutative', True),
        ('complex', True),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('negative', False),
        ('real', True),
       ) ),
     ),
    # Implications of nonnegative = False:
    (('nonnegative', False), set( (
        ('composite', False),
        ('positive', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of nonpositive = True:
    (('nonpositive', True), set( (
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('extended_nonpositive', True),
        ('extended_positive', False),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('positive', False),
        ('prime', False),
        ('real', True),
       ) ),
     ),
    # Implications of nonpositive = False:
    (('nonpositive', False), set( (
        ('negative', False),
        ('zero', False),
       ) ),
     ),
    # Implications of nonzero = True:
    (('nonzero', True), set( (
        ('commutative', True),
        ('complex', True),
        ('extended_nonzero', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('real', True),
        ('zero', False),
       ) ),
     ),
    # Implications of nonzero = False:
    (('nonzero', False), set( (
        ('composite', False),
        ('negative', False),
        ('positive', False),
        ('prime', False),
       ) ),
     ),
    # Implications of odd = True:
    (('odd', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('even', False),
        ('extended_nonzero', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', True),
        ('irrational', False),
        ('noninteger', False),
        ('nonzero', True),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of odd = False:
    (('odd', False), set( (
       ) ),
     ),
    # Implications of positive = True:
    (('positive', True), set( (
        ('commutative', True),
        ('complex', True),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_nonpositive', False),
        ('extended_nonzero', True),
        ('extended_positive', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('negative', False),
        ('nonnegative', True),
        ('nonpositive', False),
        ('nonzero', True),
        ('real', True),
        ('zero', False),
       ) ),
     ),
    # Implications of positive = False:
    (('positive', False), set( (
        ('composite', False),
        ('prime', False),
       ) ),
     ),
    # Implications of prime = True:
    (('prime', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_nonpositive', False),
        ('extended_nonzero', True),
        ('extended_positive', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', True),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', True),
        ('nonpositive', False),
        ('nonzero', True),
        ('positive', True),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
        ('zero', False),
       ) ),
     ),
    # Implications of prime = False:
    (('prime', False), set( (
       ) ),
     ),
    # Implications of rational = True:
    (('rational', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('irrational', False),
        ('real', True),
        ('transcendental', False),
       ) ),
     ),
    # Implications of rational = False:
    (('rational', False), set( (
        ('composite', False),
        ('even', False),
        ('integer', False),
        ('odd', False),
        ('prime', False),
        ('zero', False),
       ) ),
     ),
    # Implications of real = True:
    (('real', True), set( (
        ('commutative', True),
        ('complex', True),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
       ) ),
     ),
    # Implications of real = False:
    (('real', False), set( (
        ('composite', False),
        ('even', False),
        ('integer', False),
        ('irrational', False),
        ('negative', False),
        ('nonnegative', False),
        ('nonpositive', False),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', False),
        ('zero', False),
       ) ),
     ),
    # Implications of transcendental = True:
    (('transcendental', True), set( (
        ('algebraic', False),
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('even', False),
        ('finite', True),
        ('infinite', False),
        ('integer', False),
        ('odd', False),
        ('prime', False),
        ('rational', False),
        ('zero', False),
       ) ),
     ),
    # Implications of transcendental = False:
    (('transcendental', False), set( (
       ) ),
     ),
    # Implications of zero = True:
    (('zero', True), set( (
        ('algebraic', True),
        ('commutative', True),
        ('complex', True),
        ('composite', False),
        ('even', True),
        ('extended_negative', False),
        ('extended_nonnegative', True),
        ('extended_nonpositive', True),
        ('extended_nonzero', False),
        ('extended_positive', False),
        ('extended_real', True),
        ('finite', True),
        ('hermitian', True),
        ('imaginary', False),
        ('infinite', False),
        ('integer', True),
        ('irrational', False),
        ('negative', False),
        ('noninteger', False),
        ('nonnegative', True),
        ('nonpositive', True),
        ('nonzero', False),
        ('odd', False),
        ('positive', False),
        ('prime', False),
        ('rational', True),
        ('real', True),
        ('transcendental', False),
       ) ),
     ),
    # Implications of zero = False:
    (('zero', False), set( (
       ) ),
     ),
 ] ) # full_implications


prereq = {

    # facts that could determine the value of algebraic
    'algebraic': {
        'commutative',
        'complex',
        'composite',
        'even',
        'finite',
        'infinite',
        'integer',
        'odd',
        'prime',
        'rational',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of antihermitian
    'antihermitian': {
        'imaginary',
    },

    # facts that could determine the value of commutative
    'commutative': {
        'algebraic',
        'complex',
        'composite',
        'even',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'imaginary',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of complex
    'complex': {
        'algebraic',
        'commutative',
        'composite',
        'even',
        'finite',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of composite
    'composite': {
        'algebraic',
        'commutative',
        'complex',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of even
    'even': {
        'algebraic',
        'commutative',
        'complex',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'noninteger',
        'odd',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of extended_negative
    'extended_negative': {
        'commutative',
        'composite',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'imaginary',
        'negative',
        'nonnegative',
        'positive',
        'prime',
        'zero',
    },

    # facts that could determine the value of extended_nonnegative
    'extended_nonnegative': {
        'commutative',
        'composite',
        'extended_negative',
        'extended_positive',
        'extended_real',
        'imaginary',
        'negative',
        'nonnegative',
        'positive',
        'prime',
        'zero',
    },

    # facts that could determine the value of extended_nonpositive
    'extended_nonpositive': {
        'commutative',
        'composite',
        'extended_negative',
        'extended_positive',
        'extended_real',
        'imaginary',
        'negative',
        'nonpositive',
        'positive',
        'prime',
        'zero',
    },

    # facts that could determine the value of extended_nonzero
    'extended_nonzero': {
        'commutative',
        'composite',
        'extended_negative',
        'extended_positive',
        'extended_real',
        'imaginary',
        'irrational',
        'negative',
        'noninteger',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'zero',
    },

    # facts that could determine the value of extended_positive
    'extended_positive': {
        'commutative',
        'composite',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_real',
        'imaginary',
        'negative',
        'nonpositive',
        'positive',
        'prime',
        'zero',
    },

    # facts that could determine the value of extended_real
    'extended_real': {
        'commutative',
        'composite',
        'even',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'imaginary',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'zero',
    },

    # facts that could determine the value of finite
    'finite': {
        'algebraic',
        'complex',
        'composite',
        'even',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of hermitian
    'hermitian': {
        'composite',
        'even',
        'integer',
        'irrational',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'zero',
    },

    # facts that could determine the value of imaginary
    'imaginary': {
        'antihermitian',
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'zero',
    },

    # facts that could determine the value of infinite
    'infinite': {
        'algebraic',
        'complex',
        'composite',
        'even',
        'finite',
        'imaginary',
        'integer',
        'irrational',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of integer
    'integer': {
        'algebraic',
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'irrational',
        'noninteger',
        'odd',
        'prime',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of irrational
    'irrational': {
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'odd',
        'prime',
        'rational',
        'real',
        'zero',
    },

    # facts that could determine the value of negative
    'negative': {
        'commutative',
        'complex',
        'composite',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'positive',
        'prime',
        'real',
        'zero',
    },

    # facts that could determine the value of noninteger
    'noninteger': {
        'commutative',
        'composite',
        'even',
        'extended_real',
        'imaginary',
        'integer',
        'irrational',
        'odd',
        'prime',
        'zero',
    },

    # facts that could determine the value of nonnegative
    'nonnegative': {
        'commutative',
        'complex',
        'composite',
        'extended_negative',
        'extended_nonnegative',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'negative',
        'positive',
        'prime',
        'real',
        'zero',
    },

    # facts that could determine the value of nonpositive
    'nonpositive': {
        'commutative',
        'complex',
        'composite',
        'extended_nonpositive',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'negative',
        'positive',
        'prime',
        'real',
        'zero',
    },

    # facts that could determine the value of nonzero
    'nonzero': {
        'commutative',
        'complex',
        'composite',
        'extended_nonzero',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'irrational',
        'negative',
        'odd',
        'positive',
        'prime',
        'real',
        'zero',
    },

    # facts that could determine the value of odd
    'odd': {
        'algebraic',
        'commutative',
        'complex',
        'even',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'noninteger',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of positive
    'positive': {
        'commutative',
        'complex',
        'composite',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'prime',
        'real',
        'zero',
    },

    # facts that could determine the value of prime
    'prime': {
        'algebraic',
        'commutative',
        'complex',
        'composite',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'positive',
        'rational',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of rational
    'rational': {
        'algebraic',
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'odd',
        'prime',
        'real',
        'transcendental',
        'zero',
    },

    # facts that could determine the value of real
    'real': {
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'zero',
    },

    # facts that could determine the value of transcendental
    'transcendental': {
        'algebraic',
        'commutative',
        'complex',
        'composite',
        'even',
        'finite',
        'infinite',
        'integer',
        'odd',
        'prime',
        'rational',
        'zero',
    },

    # facts that could determine the value of zero
    'zero': {
        'algebraic',
        'commutative',
        'complex',
        'composite',
        'even',
        'extended_negative',
        'extended_nonnegative',
        'extended_nonpositive',
        'extended_nonzero',
        'extended_positive',
        'extended_real',
        'finite',
        'hermitian',
        'imaginary',
        'infinite',
        'integer',
        'irrational',
        'negative',
        'noninteger',
        'nonnegative',
        'nonpositive',
        'nonzero',
        'odd',
        'positive',
        'prime',
        'rational',
        'real',
        'transcendental',
    },

} # prereq


# Note: the order of the beta rules is used in the beta_triggers
beta_rules = [

    # Rules implying composite = True
    ({('even', True), ('positive', True), ('prime', False)},
        ('composite', True)),

    # Rules implying even = False
    ({('composite', False), ('positive', True), ('prime', False)},
        ('even', False)),

    # Rules implying even = True
    ({('integer', True), ('odd', False)},
        ('even', True)),

    # Rules implying extended_negative = True
    ({('extended_positive', False), ('extended_real', True), ('zero', False)},
        ('extended_negative', True)),
    ({('extended_nonpositive', True), ('extended_nonzero', True)},
        ('extended_negative', True)),

    # Rules implying extended_nonnegative = True
    ({('extended_negative', False), ('extended_real', True)},
        ('extended_nonnegative', True)),

    # Rules implying extended_nonpositive = True
    ({('extended_positive', False), ('extended_real', True)},
        ('extended_nonpositive', True)),

    # Rules implying extended_nonzero = True
    ({('extended_real', True), ('zero', False)},
        ('extended_nonzero', True)),

    # Rules implying extended_positive = True
    ({('extended_negative', False), ('extended_real', True), ('zero', False)},
        ('extended_positive', True)),
    ({('extended_nonnegative', True), ('extended_nonzero', True)},
        ('extended_positive', True)),

    # Rules implying extended_real = False
    ({('infinite', False), ('real', False)},
        ('extended_real', False)),
    ({('extended_negative', False), ('extended_positive', False), ('zero', False)},
        ('extended_real', False)),

    # Rules implying infinite = True
    ({('extended_real', True), ('real', False)},
        ('infinite', True)),

    # Rules implying irrational = True
    ({('rational', False), ('real', True)},
        ('irrational', True)),

    # Rules implying negative = True
    ({('positive', False), ('real', True), ('zero', False)},
        ('negative', True)),
    ({('nonpositive', True), ('nonzero', True)},
        ('negative', True)),
    ({('extended_negative', True), ('finite', True)},
        ('negative', True)),

    # Rules implying noninteger = True
    ({('extended_real', True), ('integer', False)},
        ('noninteger', True)),

    # Rules implying nonnegative = True
    ({('negative', False), ('real', True)},
        ('nonnegative', True)),
    ({('extended_nonnegative', True), ('finite', True)},
        ('nonnegative', True)),

    # Rules implying nonpositive = True
    ({('positive', False), ('real', True)},
        ('nonpositive', True)),
    ({('extended_nonpositive', True), ('finite', True)},
        ('nonpositive', True)),

    # Rules implying nonzero = True
    ({('extended_nonzero', True), ('finite', True)},
        ('nonzero', True)),

    # Rules implying odd = True
    ({('even', False), ('integer', True)},
        ('odd', True)),

    # Rules implying positive = False
    ({('composite', False), ('even', True), ('prime', False)},
        ('positive', False)),

    # Rules implying positive = True
    ({('negative', False), ('real', True), ('zero', False)},
        ('positive', True)),
    ({('nonnegative', True), ('nonzero', True)},
        ('positive', True)),
    ({('extended_positive', True), ('finite', True)},
        ('positive', True)),

    # Rules implying prime = True
    ({('composite', False), ('even', True), ('positive', True)},
        ('prime', True)),

    # Rules implying real = False
    ({('negative', False), ('positive', False), ('zero', False)},
        ('real', False)),

    # Rules implying real = True
    ({('extended_real', True), ('infinite', False)},
        ('real', True)),
    ({('extended_real', True), ('finite', True)},
        ('real', True)),

    # Rules implying transcendental = True
    ({('algebraic', False), ('complex', True)},
        ('transcendental', True)),

    # Rules implying zero = True
    ({('extended_negative', False), ('extended_positive', False), ('extended_real', True)},
        ('zero', True)),
    ({('negative', False), ('positive', False), ('real', True)},
        ('zero', True)),
    ({('extended_nonnegative', True), ('extended_nonpositive', True)},
        ('zero', True)),
    ({('nonnegative', True), ('nonpositive', True)},
        ('zero', True)),

] # beta_rules
beta_triggers = {
    ('algebraic', False): [32, 11, 3, 8, 29, 14, 25, 13, 17, 7],
    ('algebraic', True): [10, 30, 31, 27, 16, 21, 19, 22],
    ('antihermitian', False): [],
    ('commutative', False): [],
    ('complex', False): [10, 12, 11, 3, 8, 17, 7],
    ('complex', True): [32, 10, 30, 31, 27, 16, 21, 19, 22],
    ('composite', False): [1, 28, 24],
    ('composite', True): [23, 2],
    ('even', False): [23, 11, 3, 8, 29, 14, 25, 7],
    ('even', True): [3, 33, 8, 6, 5, 14, 34, 25, 20, 18, 27, 16, 21, 19, 22, 0, 28, 24, 7],
    ('extended_negative', False): [11, 33, 8, 5, 29, 34, 25, 18],
    ('extended_negative', True): [30, 12, 31, 29, 14, 20, 16, 21, 22, 17],
    ('extended_nonnegative', False): [11, 3, 6, 29, 14, 20, 7],
    ('extended_nonnegative', True): [30, 12, 31, 33, 8, 9, 6, 29, 34, 25, 18, 19, 35, 17, 7],
    ('extended_nonpositive', False): [11, 8, 5, 29, 25, 18, 7],
    ('extended_nonpositive', True): [30, 12, 31, 3, 33, 4, 5, 29, 14, 34, 20, 21, 35, 17, 7],
    ('extended_nonzero', False): [11, 33, 6, 5, 29, 34, 20, 18],
    ('extended_nonzero', True): [30, 12, 31, 3, 8, 4, 9, 6, 5, 29, 14, 25, 22, 17],
    ('extended_positive', False): [11, 3, 33, 6, 29, 14, 34, 20],
    ('extended_positive', True): [30, 12, 31, 29, 25, 18, 27, 19, 22, 17],
    ('extended_real', False): [],
    ('extended_real', True): [30, 12, 31, 3, 33, 8, 6, 5, 17, 7],
    ('finite', False): [11, 3, 8, 17, 7],
    ('finite', True): [10, 30, 31, 27, 16, 21, 19, 22],
    ('hermitian', False): [10, 12, 11, 3, 8, 17, 7],
    ('imaginary', True): [32],
    ('infinite', False): [10, 30, 31, 27, 16, 21, 19, 22],
    ('infinite', True): [11, 3, 8, 17, 7],
    ('integer', False): [11, 3, 8, 29, 14, 25, 17, 7],
    ('integer', True): [23, 2, 3, 33, 8, 6, 5, 14, 34, 25, 20, 18, 27, 16, 21, 19, 22, 7],
    ('irrational', True): [32, 3, 8, 4, 9, 6, 5, 14, 25, 15, 26, 20, 18, 27, 16, 21, 19],
    ('negative', False): [29, 34, 25, 18],
    ('negative', True): [32, 13, 17],
    ('noninteger', True): [30, 12, 31, 3, 8, 4, 9, 6, 5, 29, 14, 25, 22],
    ('nonnegative', False): [11, 3, 8, 29, 14, 20, 7],
    ('nonnegative', True): [32, 33, 8, 9, 6, 34, 25, 26, 20, 27, 21, 22, 35, 36, 13, 17, 7],
    ('nonpositive', False): [11, 3, 8, 29, 25, 18, 7],
    ('nonpositive', True): [32, 3, 33, 4, 5, 14, 34, 15, 18, 16, 19, 22, 35, 36, 13, 17, 7],
    ('nonzero', False): [29, 34, 20, 18],
    ('nonzero', True): [32, 3, 8, 4, 9, 6, 5, 14, 25, 15, 26, 20, 18, 27, 16, 21, 19, 13, 17],
    ('odd', False): [2],
    ('odd', True): [3, 8, 4, 9, 6, 5, 14, 25, 15, 26, 20, 18, 27, 16, 21, 19],
    ('positive', False): [29, 14, 34, 20],
    ('positive', True): [32, 0, 1, 28, 13, 17],
    ('prime', False): [0, 1, 24],
    ('prime', True): [23, 2],
    ('rational', False): [11, 3, 8, 29, 14, 25, 13, 17, 7],
    ('rational', True): [3, 33, 8, 6, 5, 14, 34, 25, 20, 18, 27, 16, 21, 19, 22, 17, 7],
    ('real', False): [10, 12, 11, 3, 8, 17, 7],
    ('real', True): [32, 3, 33, 8, 6, 5, 14, 34, 25, 20, 18, 27, 16, 21, 19, 22, 13, 17, 7],
    ('transcendental', True): [10, 30, 31, 11, 3, 8, 29, 14, 25, 27, 16, 21, 19, 22, 13, 17, 7],
    ('zero', False): [11, 3, 8, 29, 14, 25, 7],
    ('zero', True): [],
} # beta_triggers


generated_assumptions = {'defined_facts': defined_facts, 'full_implications': full_implications,
               'prereq': prereq, 'beta_rules': beta_rules, 'beta_triggers': beta_triggers}
