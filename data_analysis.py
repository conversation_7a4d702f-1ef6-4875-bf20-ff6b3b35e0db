"""
Data Analysis Module - Interactive data analysis with Streamlit UI

This module provides interactive data analysis capabilities with a Streamlit UI.
It includes functions for clustering, outlier detection, trend analysis, and correlation analysis.
"""

import streamlit as st
import pandas as pd
import numpy as np
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
from scipy import stats
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, Any, List, Tuple, Optional, Union, Protocol
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path
from abc import ABC, abstractmethod
import concurrent.futures
from functools import lru_cache
import seaborn as sns
from scipy.stats import skew, kurtosis
import warnings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore')

class AnalysisError(Exception):
    """Base exception class for analysis errors."""
    pass

class ValidationError(AnalysisError):
    """Exception raised for data validation errors."""
    pass

class ProcessingError(AnalysisError):
    """Exception raised for data processing errors."""
    pass

class DataQualityError(AnalysisError):
    """Exception raised for data quality issues."""
    pass

class AnalysisCategory(Enum):
    """Enum for analysis categories."""
    CLUSTERING = "Clustering"
    OUTLIER_DETECTION = "Outlier Detection"
    TREND_ANALYSIS = "Trend Analysis"
    CORRELATION_ANALYSIS = "Correlation Analysis"

class AnalysisMethod(Enum):
    """Enum for analysis methods."""
    # Clustering methods
    KMEANS = "K-means"
    DBSCAN = "DBSCAN"
    HIERARCHICAL = "Hierarchical"
    
    # Outlier detection methods
    ISOLATION_FOREST = "Isolation Forest"
    ZSCORE = "Z-score"
    IQR = "IQR"
    
    # Trend analysis methods
    MOVING_AVERAGE = "Moving Average"
    EXPONENTIAL_SMOOTHING = "Exponential Smoothing"
    POLYNOMIAL_REGRESSION = "Polynomial Regression"
    
    # Correlation analysis methods
    PEARSON = "Pearson"
    SPEARSON = "Spearman"
    KENDALL = "Kendall"

@dataclass
class AnalysisConfig:
    """Configuration for data analysis."""
    category: AnalysisCategory
    method: AnalysisMethod
    features: List[str]
    options: Dict[str, Any]

class AnalysisStrategy(ABC):
    """Abstract base class for analysis strategies."""
    
    @abstractmethod
    def validate_input(self, df: pd.DataFrame, options: Dict[str, Any]) -> None:
        """Validate input data and options."""
        pass
    
    @abstractmethod
    def perform_analysis(self, df: pd.DataFrame, options: Dict[str, Any]) -> Tuple[pd.DataFrame, go.Figure]:
        """Perform the analysis and return results."""
        pass

class ClusteringStrategy(AnalysisStrategy):
    """Strategy for clustering analysis."""
    
    def validate_input(self, df: pd.DataFrame, options: Dict[str, Any]) -> None:
        features = options.get("features", [])
        if not features:
            raise ValidationError("No features selected for clustering")
        if len(features) < 2:
            raise ValidationError("At least 2 features required for clustering")
        if not all(f in df.columns for f in features):
            raise ValidationError("Selected features not found in DataFrame")
    
    @st.cache_data
    def perform_analysis(self, df: pd.DataFrame, options: Dict[str, Any]) -> Tuple[pd.DataFrame, go.Figure]:
        features = options["features"]
        X = df[features].values
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        method = options["method"]
        if method == AnalysisMethod.KMEANS.value:
            kmeans = KMeans(
                n_clusters=options["n_clusters"],
                random_state=42
            )
            labels = kmeans.fit_predict(X_scaled)
            centers = kmeans.cluster_centers_
            
        elif method == AnalysisMethod.DBSCAN.value:
            dbscan = DBSCAN(
                eps=options["eps"],
                min_samples=options["min_samples"]
            )
            labels = dbscan.fit_predict(X_scaled)
            centers = None
        else:
            raise ProcessingError(f"Unsupported clustering method: {method}")
        
        # Create multiple visualizations
        figures = {}
        
        # 2D scatter plot
        fig_2d = go.Figure()
        for label in np.unique(labels):
            mask = labels == label
            fig_2d.add_trace(go.Scatter(
                x=X[mask, 0],
                y=X[mask, 1],
                mode="markers",
                name=f"Cluster {label}",
                marker=dict(size=8)
            ))
        
        if centers is not None:
            fig_2d.add_trace(go.Scatter(
                x=centers[:, 0],
                y=centers[:, 1],
                mode="markers",
                name="Cluster Centers",
                marker=dict(
                    size=12,
                    symbol="star",
                    color="black"
                )
            ))
        
        fig_2d.update_layout(
            title=f"{method} Clustering Results (2D)",
            xaxis_title=features[0],
            yaxis_title=features[1],
            showlegend=True
        )
        figures["2d_scatter"] = fig_2d
        
        # 3D scatter plot if we have 3 or more features
        if len(features) >= 3:
            fig_3d = go.Figure()
            for label in np.unique(labels):
                mask = labels == label
                fig_3d.add_trace(go.Scatter3d(
                    x=X[mask, 0],
                    y=X[mask, 1],
                    z=X[mask, 2],
                    mode="markers",
                    name=f"Cluster {label}",
                    marker=dict(size=8)
                ))
            
            if centers is not None:
                fig_3d.add_trace(go.Scatter3d(
                    x=centers[:, 0],
                    y=centers[:, 1],
                    z=centers[:, 2],
                    mode="markers",
                    name="Cluster Centers",
                    marker=dict(
                        size=12,
                        symbol="star",
                        color="black"
                    )
                ))
            
            fig_3d.update_layout(
                title=f"{method} Clustering Results (3D)",
                scene=dict(
                    xaxis_title=features[0],
                    yaxis_title=features[1],
                    zaxis_title=features[2]
                ),
                showlegend=True
            )
            figures["3d_scatter"] = fig_3d
        
        # Cluster size distribution
        cluster_sizes = pd.Series(labels).value_counts().sort_index()
        fig_sizes = go.Figure(data=go.Bar(
            x=cluster_sizes.index,
            y=cluster_sizes.values,
            text=cluster_sizes.values,
            textposition='auto',
        ))
        fig_sizes.update_layout(
            title="Cluster Size Distribution",
            xaxis_title="Cluster",
            yaxis_title="Number of Points",
            showlegend=False
        )
        figures["cluster_sizes"] = fig_sizes
        
        # Silhouette plot if we have more than one cluster
        if len(np.unique(labels)) > 1:
            from sklearn.metrics import silhouette_samples
            silhouette_vals = silhouette_samples(X_scaled, labels)
            
            fig_silhouette = go.Figure()
            y_lower = 0
            
            for label in np.unique(labels):
                mask = labels == label
                cluster_silhouette_vals = silhouette_vals[mask]
                cluster_silhouette_vals.sort()
                
                y_upper = y_lower + len(cluster_silhouette_vals)
                y_ticks = np.arange(y_lower, y_upper)
                
                fig_silhouette.add_trace(go.Scatter(
                    x=cluster_silhouette_vals,
                    y=y_ticks,
                    mode="lines",
                    name=f"Cluster {label}",
                    line=dict(width=1)
                ))
                
                y_lower = y_upper + 10
            
            fig_silhouette.update_layout(
                title="Silhouette Plot",
                xaxis_title="Silhouette Coefficient",
                yaxis_title="Cluster",
                showlegend=True
            )
            figures["silhouette"] = fig_silhouette
        
        # Add cluster labels to DataFrame
        df_result = df.copy()
        df_result["cluster"] = labels
        
        return df_result, figures

class OutlierDetectionStrategy(AnalysisStrategy):
    """Strategy for outlier detection analysis."""
    
    def validate_input(self, df: pd.DataFrame, options: Dict[str, Any]) -> None:
        features = options.get("features", [])
        if not features:
            raise ValidationError("No features selected for outlier detection")
        if len(features) < 2:
            raise ValidationError("At least 2 features required for outlier detection")
        if not all(f in df.columns for f in features):
            raise ValidationError("Selected features not found in DataFrame")
    
    @st.cache_data
    def perform_analysis(self, df: pd.DataFrame, options: Dict[str, Any]) -> Tuple[pd.DataFrame, go.Figure]:
        features = options["features"]
        X = df[features].values
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        method = options["method"]
        if method == AnalysisMethod.ISOLATION_FOREST.value:
            iso_forest = IsolationForest(
                contamination=options["contamination"],
                random_state=42
            )
            labels = iso_forest.fit_predict(X_scaled)
            scores = -iso_forest.score_samples(X_scaled)
            
        elif method == AnalysisMethod.ZSCORE.value:
            z_scores = np.abs(stats.zscore(X_scaled))
            labels = np.any(z_scores > options["threshold"], axis=1).astype(int)
            scores = np.max(z_scores, axis=1)
            
        elif method == AnalysisMethod.IQR.value:
            Q1 = np.percentile(X_scaled, 25, axis=0)
            Q3 = np.percentile(X_scaled, 75, axis=0)
            IQR = Q3 - Q1
            outlier_mask = np.any(
                (X_scaled < Q1 - options["threshold"] * IQR) |
                (X_scaled > Q3 + options["threshold"] * IQR),
                axis=1
            )
            labels = outlier_mask.astype(int)
            scores = np.max(
                np.abs(X_scaled - np.median(X_scaled, axis=0)) / IQR,
                axis=1
            )
        else:
            raise ProcessingError(f"Unsupported outlier detection method: {method}")
        
        # Create visualization
        fig = go.Figure()
        
        # Add scatter points
        fig.add_trace(go.Scatter(
            x=X[labels == 1, 0],
            y=X[labels == 1, 1],
            mode="markers",
            name="Normal",
            marker=dict(size=8)
        ))
        
        fig.add_trace(go.Scatter(
            x=X[labels == -1, 0],
            y=X[labels == -1, 1],
            mode="markers",
            name="Outlier",
            marker=dict(
                size=8,
                color="red",
                symbol="x"
            )
        ))
        
        # Update layout
        fig.update_layout(
            title=f"{method} Outlier Detection Results",
            xaxis_title=features[0],
            yaxis_title=features[1],
            showlegend=True
        )
        
        # Add outlier labels and scores to DataFrame
        df_result = df.copy()
        df_result["is_outlier"] = labels == -1
        df_result["outlier_score"] = scores
        
        return df_result, fig

class TrendAnalysisStrategy(AnalysisStrategy):
    """Strategy for trend analysis."""
    
    def validate_input(self, df: pd.DataFrame, options: Dict[str, Any]) -> None:
        x_col = options.get("x_col")
        y_col = options.get("y_col")
        if not x_col or not y_col:
            raise ValidationError("X and Y columns must be selected for trend analysis")
        if x_col not in df.columns or y_col not in df.columns:
            raise ValidationError("Selected columns not found in DataFrame")
    
    @st.cache_data
    def perform_analysis(self, df: pd.DataFrame, options: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, go.Figure]]:
        x_col = options["x_col"]
        y_col = options["y_col"]
        X = df[x_col].values.reshape(-1, 1)
        y = df[y_col].values
        
        method = options["method"]
        if method == AnalysisMethod.MOVING_AVERAGE.value:
            window = options["window"]
            y_smooth = pd.Series(y).rolling(window=window).mean().values
            
        elif method == AnalysisMethod.EXPONENTIAL_SMOOTHING.value:
            alpha = options["alpha"]
            y_smooth = pd.Series(y).ewm(alpha=alpha).mean().values
            
        elif method == AnalysisMethod.POLYNOMIAL_REGRESSION.value:
            degree = options["degree"]
            coeffs = np.polyfit(X.ravel(), y, degree)
            y_smooth = np.polyval(coeffs, X.ravel())
        else:
            raise ProcessingError(f"Unsupported trend analysis method: {method}")
        
        # Create multiple visualizations
        figures = {}
        
        # Main trend plot with confidence intervals
        fig_trend = go.Figure()
        
        # Add original data with hover information
        fig_trend.add_trace(go.Scatter(
            x=X.ravel(),
            y=y,
            mode="markers",
            name="Original Data",
            marker=dict(size=8),
            hovertemplate="<b>%{x}</b><br>" +
                         f"{y_col}: %{{y:.2f}}<br>" +
                         "<extra></extra>"
        ))
        
        # Add trend line with confidence intervals
        if method == AnalysisMethod.POLYNOMIAL_REGRESSION.value:
            # Calculate confidence intervals
            y_err = np.std(y - y_smooth)
            fig_trend.add_trace(go.Scatter(
                x=X.ravel(),
                y=y_smooth + 2*y_err,
                mode="lines",
                line=dict(width=0),
                showlegend=False,
                hoverinfo="skip"
            ))
            fig_trend.add_trace(go.Scatter(
                x=X.ravel(),
                y=y_smooth - 2*y_err,
                mode="lines",
                line=dict(width=0),
                fill="tonexty",
                fillcolor="rgba(0,100,80,0.2)",
                name="95% Confidence Interval",
                hoverinfo="skip"
            ))
        
        # Add trend line
        fig_trend.add_trace(go.Scatter(
            x=X.ravel(),
            y=y_smooth,
            mode="lines",
            name="Trend",
            line=dict(width=2),
            hovertemplate="<b>%{x}</b><br>" +
                         f"Trend: %{{y:.2f}}<br>" +
                         "<extra></extra>"
        ))
        
        # Update layout with interactive features
        fig_trend.update_layout(
            title=f"{method} Trend Analysis Results",
            xaxis_title=x_col,
            yaxis_title=y_col,
            showlegend=True,
            hovermode="closest",
            hoverdistance=100,
            spikedistance=1000,
            xaxis=dict(
                showspikes=True,
                spikecolor="grey",
                spikesnap="cursor",
                spikemode="across",
                spikedash="solid",
                spikethickness=1
            ),
            yaxis=dict(
                showspikes=True,
                spikecolor="grey",
                spikesnap="cursor",
                spikemode="across",
                spikedash="solid",
                spikethickness=1
            )
        )
        figures["trend"] = fig_trend
        
        # Residual plot
        residuals = y - y_smooth
        fig_residuals = go.Figure()
        fig_residuals.add_trace(go.Scatter(
            x=X.ravel(),
            y=residuals,
            mode="markers",
            name="Residuals",
            marker=dict(size=8),
            hovertemplate="<b>%{x}</b><br>" +
                         f"Residual: %{{y:.2f}}<br>" +
                         "<extra></extra>"
        ))
        fig_residuals.add_trace(go.Scatter(
            x=X.ravel(),
            y=[0] * len(X),
            mode="lines",
            name="Zero Line",
            line=dict(color="red", dash="dash")
        ))
        fig_residuals.update_layout(
            title="Residual Plot",
            xaxis_title=x_col,
            yaxis_title="Residuals",
            showlegend=True,
            hovermode="closest"
        )
        figures["residuals"] = fig_residuals
        
        # Distribution of residuals
        fig_residual_dist = go.Figure()
        fig_residual_dist.add_trace(go.Histogram(
            x=residuals,
            name="Residuals",
            nbinsx=30,
            histnorm="probability"
        ))
        fig_residual_dist.add_trace(go.Scatter(
            x=np.sort(residuals),
            y=stats.norm.pdf(np.sort(residuals), np.mean(residuals), np.std(residuals)),
            name="Normal Distribution",
            line=dict(color="red")
        ))
        fig_residual_dist.update_layout(
            title="Distribution of Residuals",
            xaxis_title="Residual Value",
            yaxis_title="Probability",
            showlegend=True
        )
        figures["residual_distribution"] = fig_residual_dist
        
        # Rolling statistics
        if method == AnalysisMethod.MOVING_AVERAGE.value:
            # Rolling mean and standard deviation
            rolling_mean = pd.Series(y).rolling(window=window).mean()
            rolling_std = pd.Series(y).rolling(window=window).std()
            
            fig_rolling = go.Figure()
            fig_rolling.add_trace(go.Scatter(
                x=X.ravel(),
                y=y,
                mode="markers",
                name="Original Data",
                marker=dict(size=8),
                opacity=0.5
            ))
            fig_rolling.add_trace(go.Scatter(
                x=X.ravel(),
                y=rolling_mean,
                mode="lines",
                name="Rolling Mean",
                line=dict(width=2)
            ))
            fig_rolling.add_trace(go.Scatter(
                x=X.ravel(),
                y=rolling_mean + 2*rolling_std,
                mode="lines",
                line=dict(width=1, dash="dash"),
                name="Upper Bound",
                hoverinfo="skip"
            ))
            fig_rolling.add_trace(go.Scatter(
                x=X.ravel(),
                y=rolling_mean - 2*rolling_std,
                mode="lines",
                line=dict(width=1, dash="dash"),
                name="Lower Bound",
                fill="tonexty",
                fillcolor="rgba(0,100,80,0.2)",
                hoverinfo="skip"
            ))
            fig_rolling.update_layout(
                title="Rolling Statistics",
                xaxis_title=x_col,
                yaxis_title=y_col,
                showlegend=True,
                hovermode="closest"
            )
            figures["rolling_stats"] = fig_rolling
        
        # Add trend values and residuals to DataFrame
        df_result = df.copy()
        df_result["trend"] = y_smooth
        df_result["residual"] = residuals
        
        return df_result, figures

class CorrelationAnalysisStrategy(AnalysisStrategy):
    """Strategy for correlation analysis."""
    
    def validate_input(self, df: pd.DataFrame, options: Dict[str, Any]) -> None:
        features = options.get("features", [])
        if not features:
            raise ValidationError("No features selected for correlation analysis")
        if len(features) < 2:
            raise ValidationError("At least 2 features required for correlation analysis")
        if not all(f in df.columns for f in features):
            raise ValidationError("Selected features not found in DataFrame")
    
    @st.cache_data
    def perform_analysis(self, df: pd.DataFrame, options: Dict[str, Any]) -> Tuple[pd.DataFrame, go.Figure]:
        features = options["features"]
        method = options["method"].lower()
        corr_matrix = df[features].corr(method=method)
        
        # Create visualization
        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.columns,
            colorscale="RdBu",
            zmin=-1,
            zmax=1
        ))
        
        # Update layout
        fig.update_layout(
            title=f"{options['method']} Correlation Analysis Results",
            xaxis_title="Features",
            yaxis_title="Features"
        )
        
        return df, fig

@st.cache_data
def validate_dataframe(
    df: pd.DataFrame,
    required_columns: Optional[List[str]] = None,
    min_rows: Optional[int] = None,
    max_rows: Optional[int] = None
) -> Tuple[bool, List[str]]:
    """
    Validate a DataFrame for analysis.
    
    Args:
        df: DataFrame to validate
        required_columns: Optional list of required columns
        min_rows: Optional minimum number of rows
        max_rows: Optional maximum number of rows
        
    Returns:
        Tuple of (is_valid, list of error messages)
    """
    errors = []
    
    # Check required columns
    if required_columns:
        missing_cols = [col for col in required_columns if col not in df.columns]
        if missing_cols:
            errors.append(f"Missing required columns: {', '.join(missing_cols)}")
    
    # Check number of rows
    if min_rows and len(df) < min_rows:
        errors.append(f"DataFrame must have at least {min_rows} rows")
    if max_rows and len(df) > max_rows:
        errors.append(f"DataFrame must have at most {max_rows} rows")
    
    # Check for missing values
    missing_values = df.isnull().sum()
    if missing_values.any():
        errors.append(f"Missing values found in columns: {', '.join(missing_values[missing_values > 0].index)}")
    
    # Check for infinite values
    inf_values = df.isin([np.inf, -np.inf]).sum()
    if inf_values.any():
        errors.append(f"Infinite values found in columns: {', '.join(inf_values[inf_values > 0].index)}")
    
    # Check for constant columns
    constant_cols = [col for col in df.columns if df[col].nunique() <= 1]
    if constant_cols:
        errors.append(f"Constant columns found: {', '.join(constant_cols)}")
    
    return len(errors) == 0, errors

def get_analysis_strategy(category: AnalysisCategory) -> AnalysisStrategy:
    """Get the appropriate analysis strategy for the given category."""
    strategies = {
        AnalysisCategory.CLUSTERING: ClusteringStrategy(),
        AnalysisCategory.OUTLIER_DETECTION: OutlierDetectionStrategy(),
        AnalysisCategory.TREND_ANALYSIS: TrendAnalysisStrategy(),
        AnalysisCategory.CORRELATION_ANALYSIS: CorrelationAnalysisStrategy()
    }
    if category not in strategies:
        raise ProcessingError(f"No strategy implemented for category: {category}")
    return strategies[category]

def perform_data_analysis(
    df: pd.DataFrame,
    options: Dict[str, Any]
) -> Tuple[pd.DataFrame, go.Figure]:
    """
    Perform data analysis based on selected options.
    
    Args:
        df: Input DataFrame
        options: Analysis options
        
    Returns:
        Tuple of (processed DataFrame, visualization figure)
        
    Raises:
        ValidationError: If data validation fails
        ProcessingError: If analysis processing fails
    """
    try:
        category = AnalysisCategory(options["category"])
        strategy = get_analysis_strategy(category)
        
        # Validate input DataFrame
        is_valid, errors = validate_dataframe(df)
        if not is_valid:
            raise ValidationError("\n".join(errors))
        
        # Validate strategy-specific input
        strategy.validate_input(df, options)
        
        # Perform analysis with progress bar
        with st.spinner(f"Performing {category.value} analysis..."):
            df_result, fig = strategy.perform_analysis(df, options)
        
        return df_result, fig
        
    except Exception as e:
        logger.error(f"Error performing analysis: {str(e)}")
        raise ProcessingError(f"Error performing analysis: {str(e)}")

def display_data_analysis_options(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Display options for data analysis.
    
    Args:
        df: Input DataFrame
        
    Returns:
        Dictionary of analysis options
        
    Raises:
        ValidationError: If data validation fails
    """
    try:
        st.subheader("Data Analysis")
        
        # Validate input DataFrame
        is_valid, errors = validate_dataframe(df)
        if not is_valid:
            raise ValidationError("\n".join(errors))
        
        # Analysis categories
        analysis_categories = {
            AnalysisCategory.CLUSTERING.value: [
                AnalysisMethod.KMEANS.value,
                AnalysisMethod.DBSCAN.value,
                AnalysisMethod.HIERARCHICAL.value
            ],
            AnalysisCategory.OUTLIER_DETECTION.value: [
                AnalysisMethod.ISOLATION_FOREST.value,
                AnalysisMethod.ZSCORE.value,
                AnalysisMethod.IQR.value
            ],
            AnalysisCategory.TREND_ANALYSIS.value: [
                AnalysisMethod.MOVING_AVERAGE.value,
                AnalysisMethod.EXPONENTIAL_SMOOTHING.value,
                AnalysisMethod.POLYNOMIAL_REGRESSION.value
            ],
            AnalysisCategory.CORRELATION_ANALYSIS.value: [
                AnalysisMethod.PEARSON.value,
                AnalysisMethod.SPEARSON.value,
                AnalysisMethod.KENDALL.value
            ]
        }
        
        # Select analysis category
        analysis_category = st.selectbox(
            "Select Analysis Category",
            list(analysis_categories.keys())
        )
        
        # Select analysis method
        analysis_method = st.selectbox(
            "Select Analysis Method",
            analysis_categories[analysis_category]
        )
        
        # Get numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        # Analysis-specific options
        options = {
            "category": analysis_category,
            "method": analysis_method
        }
        
        if analysis_category == AnalysisCategory.CLUSTERING.value:
            st.write("Clustering Options")
            if analysis_method == AnalysisMethod.KMEANS.value:
                options["n_clusters"] = st.slider(
                    "Number of Clusters",
                    min_value=2,
                    max_value=10,
                    value=3
                )
            elif analysis_method == AnalysisMethod.DBSCAN.value:
                options["eps"] = st.slider(
                    "Epsilon",
                    min_value=0.1,
                    max_value=2.0,
                    value=0.5,
                    step=0.1
                )
                options["min_samples"] = st.slider(
                    "Minimum Samples",
                    min_value=2,
                    max_value=10,
                    value=5
                )
            
            # Feature selection for clustering
            options["features"] = st.multiselect(
                "Select Features for Clustering",
                numeric_cols,
                default=numeric_cols[:2]
            )
        
        elif analysis_category == AnalysisCategory.OUTLIER_DETECTION.value:
            st.write("Outlier Detection Options")
            if analysis_method == AnalysisMethod.ISOLATION_FOREST.value:
                options["contamination"] = st.slider(
                    "Contamination",
                    min_value=0.01,
                    max_value=0.5,
                    value=0.1,
                    step=0.01
                )
            elif analysis_method in [AnalysisMethod.ZSCORE.value, AnalysisMethod.IQR.value]:
                options["threshold"] = st.slider(
                    "Threshold",
                    min_value=1.0,
                    max_value=5.0,
                    value=3.0,
                    step=0.1
                )
            
            # Feature selection for outlier detection
            options["features"] = st.multiselect(
                "Select Features for Outlier Detection",
                numeric_cols,
                default=numeric_cols[:2]
            )
        
        elif analysis_category == AnalysisCategory.TREND_ANALYSIS.value:
            st.write("Trend Analysis Options")
            if analysis_method == AnalysisMethod.MOVING_AVERAGE.value:
                options["window"] = st.slider(
                    "Window Size",
                    min_value=2,
                    max_value=20,
                    value=5
                )
            elif analysis_method == AnalysisMethod.EXPONENTIAL_SMOOTHING.value:
                options["alpha"] = st.slider(
                    "Alpha",
                    min_value=0.1,
                    max_value=1.0,
                    value=0.3,
                    step=0.1
                )
            elif analysis_method == AnalysisMethod.POLYNOMIAL_REGRESSION.value:
                options["degree"] = st.slider(
                    "Polynomial Degree",
                    min_value=1,
                    max_value=5,
                    value=2
                )
            
            # Feature selection for trend analysis
            options["x_col"] = st.selectbox("Select X-axis", numeric_cols)
            options["y_col"] = st.selectbox("Select Y-axis", numeric_cols)
        
        elif analysis_category == AnalysisCategory.CORRELATION_ANALYSIS.value:
            st.write("Correlation Analysis Options")
            # Feature selection for correlation analysis
            options["features"] = st.multiselect(
                "Select Features for Correlation Analysis",
                numeric_cols,
                default=numeric_cols
            )
        
        return options
        
    except Exception as e:
        logger.error(f"Error displaying analysis options: {str(e)}")
        raise AnalysisError(f"Error displaying analysis options: {str(e)}")

class DataQualityMetric(Enum):
    """Enum for data quality metrics."""
    MISSING_VALUES = "Missing Values"
    DUPLICATES = "Duplicates"
    OUTLIERS = "Outliers"
    SKEWNESS = "Skewness"
    KURTOSIS = "Kurtosis"
    CORRELATION = "Correlation"
    DISTRIBUTION = "Distribution"
    CONSISTENCY = "Consistency"

@dataclass
class DataQualityReport:
    """Data quality report containing metrics and recommendations."""
    metrics: Dict[str, Any]
    recommendations: List[str]
    visualizations: Dict[str, go.Figure]

def check_data_quality(df: pd.DataFrame) -> DataQualityReport:
    """
    Perform comprehensive data quality checks.
    
    Args:
        df: Input DataFrame
        
    Returns:
        DataQualityReport containing metrics, recommendations, and visualizations
    """
    metrics = {}
    recommendations = []
    visualizations = {}
    
    # Check missing values
    missing_values = df.isnull().sum()
    missing_percentage = (missing_values / len(df)) * 100
    metrics["missing_values"] = {
        "count": missing_values.to_dict(),
        "percentage": missing_percentage.to_dict()
    }
    if missing_values.any():
        recommendations.append(
            f"Found missing values in columns: {', '.join(missing_values[missing_values > 0].index)}"
        )
    
    # Check duplicates
    duplicate_rows = df.duplicated().sum()
    metrics["duplicates"] = {
        "count": duplicate_rows,
        "percentage": (duplicate_rows / len(df)) * 100
    }
    if duplicate_rows > 0:
        recommendations.append(f"Found {duplicate_rows} duplicate rows")
    
    # Check numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        # Calculate skewness and kurtosis
        skewness = df[numeric_cols].apply(skew)
        kurt = df[numeric_cols].apply(kurtosis)
        metrics["skewness"] = skewness.to_dict()
        metrics["kurtosis"] = kurt.to_dict()
        
        # Check for extreme skewness
        highly_skewed = skewness[abs(skewness) > 2]
        if not highly_skewed.empty:
            recommendations.append(
                f"Highly skewed columns: {', '.join(highly_skewed.index)}"
            )
        
        # Create distribution plots
        for col in numeric_cols:
            fig = go.Figure()
            fig.add_trace(go.Histogram(
                x=df[col],
                name="Histogram",
                nbinsx=30
            ))
            fig.add_trace(go.Scatter(
                x=df[col].sort_values(),
                y=stats.norm.pdf(df[col].sort_values(), df[col].mean(), df[col].std()),
                name="Normal Distribution",
                line=dict(color='red')
            ))
            fig.update_layout(
                title=f"Distribution of {col}",
                xaxis_title=col,
                yaxis_title="Frequency",
                showlegend=True
            )
            visualizations[f"distribution_{col}"] = fig
    
    # Check correlations
    if len(numeric_cols) > 1:
        corr_matrix = df[numeric_cols].corr()
        metrics["correlation"] = corr_matrix.to_dict()
        
        # Create correlation heatmap
        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.columns,
            colorscale="RdBu",
            zmin=-1,
            zmax=1
        ))
        fig.update_layout(
            title="Correlation Matrix",
            xaxis_title="Features",
            yaxis_title="Features"
        )
        visualizations["correlation"] = fig
        
        # Check for high correlations
        high_corr = np.where(np.abs(corr_matrix) > 0.8)
        high_corr = [(corr_matrix.index[x], corr_matrix.columns[y], corr_matrix.iloc[x, y])
                    for x, y in zip(*high_corr) if x != y and x < y]
        if high_corr:
            recommendations.append(
                "High correlations found between: " +
                ", ".join([f"{x} and {y} ({v:.2f})" for x, y, v in high_corr])
            )
    
    # Check categorical columns
    categorical_cols = df.select_dtypes(include=['object', 'category']).columns
    if len(categorical_cols) > 0:
        for col in categorical_cols:
            value_counts = df[col].value_counts()
            metrics[f"categorical_{col}"] = {
                "unique_values": len(value_counts),
                "most_common": value_counts.head(3).to_dict()
            }
            
            # Create bar plot for categorical values
            fig = go.Figure(data=go.Bar(
                x=value_counts.index,
                y=value_counts.values,
                text=value_counts.values,
                textposition='auto',
            ))
            fig.update_layout(
                title=f"Distribution of {col}",
                xaxis_title=col,
                yaxis_title="Count",
                showlegend=False
            )
            visualizations[f"categorical_{col}"] = fig
            
            # Check for high cardinality
            if len(value_counts) > 20:
                recommendations.append(
                    f"High cardinality in column {col}: {len(value_counts)} unique values"
                )
    
    # Check for outliers using IQR method
    if len(numeric_cols) > 0:
        outliers = {}
        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            outlier_count = ((df[col] < (Q1 - 1.5 * IQR)) | (df[col] > (Q3 + 1.5 * IQR))).sum()
            outliers[col] = {
                "count": outlier_count,
                "percentage": (outlier_count / len(df)) * 100
            }
        metrics["outliers"] = outliers
        
        # Create box plots for numeric columns
        fig = go.Figure()
        for col in numeric_cols:
            fig.add_trace(go.Box(
                y=df[col],
                name=col
            ))
        fig.update_layout(
            title="Box Plots of Numeric Columns",
            yaxis_title="Value",
            showlegend=True
        )
        visualizations["boxplots"] = fig
        
        # Add recommendations for columns with many outliers
        high_outlier_cols = [col for col, stats in outliers.items() 
                           if stats["percentage"] > 5]
        if high_outlier_cols:
            recommendations.append(
                f"High number of outliers in columns: {', '.join(high_outlier_cols)}"
            )
    
    return DataQualityReport(metrics, recommendations, visualizations)

def display_data_quality_report(report: DataQualityReport) -> None:
    """
    Display the data quality report in the Streamlit UI.
    
    Args:
        report: DataQualityReport to display
    """
    st.subheader("Data Quality Report")
    
    # Display recommendations
    if report.recommendations:
        st.warning("Data Quality Issues:")
        for rec in report.recommendations:
            st.write(f"- {rec}")
    else:
        st.success("No significant data quality issues found.")
    
    # Display metrics
    st.subheader("Metrics")
    
    # Missing values
    if "missing_values" in report.metrics:
        st.write("Missing Values:")
        missing_df = pd.DataFrame(report.metrics["missing_values"])
        st.dataframe(missing_df)
    
    # Duplicates
    if "duplicates" in report.metrics:
        st.write("Duplicates:")
        st.write(f"Count: {report.metrics['duplicates']['count']}")
        st.write(f"Percentage: {report.metrics['duplicates']['percentage']:.2f}%")
    
    # Skewness and Kurtosis
    if "skewness" in report.metrics:
        st.write("Skewness and Kurtosis:")
        stats_df = pd.DataFrame({
            "Skewness": report.metrics["skewness"],
            "Kurtosis": report.metrics["kurtosis"]
        })
        st.dataframe(stats_df)
    
    # Display visualizations
    st.subheader("Visualizations")
    
    # Correlation heatmap
    if "correlation" in report.visualizations:
        st.plotly_chart(report.visualizations["correlation"])
    
    # Box plots
    if "boxplots" in report.visualizations:
        st.plotly_chart(report.visualizations["boxplots"])
    
    # Distribution plots
    for key, fig in report.visualizations.items():
        if key.startswith("distribution_"):
            st.plotly_chart(fig)
    
    # Categorical plots
    for key, fig in report.visualizations.items():
        if key.startswith("categorical_"):
            st.plotly_chart(fig)
