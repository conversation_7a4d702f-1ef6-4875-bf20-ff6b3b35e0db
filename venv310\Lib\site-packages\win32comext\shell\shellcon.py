# Generated by h2py from \mssdk\include\shlobj.h and shellapi.h
WM_USER = 1024
DROPEFFECT_NONE = 0
DROPEFFECT_COPY = 1
DROPEFFECT_MOVE = 2
DROPEFFECT_LINK = 4
DROPEFFECT_SCROLL = -2147483648

FO_MOVE = 1
FO_COPY = 2
FO_DELETE = 3
FO_RENAME = 4

## File operation flags used with shell.SHFileOperation
FOF_MULTIDESTFILES = 1
FOF_CONFIRMMOUSE = 2
FOF_SILENT = 4
FOF_RENAMEONCOLLISION = 8
FOF_NOCONFIRMATION = 16
FOF_WANTMAPPINGHANDLE = 32
FOF_ALLOWUNDO = 64
FOF_FILESONLY = 128
FOF_SIMPLEPROGRESS = 256
FOF_NOCONFIRMMKDIR = 512
FOF_NOERRORUI = 1024
FOF_NOCOPYSECURITYATTRIBS = 2048
FOF_NORECURSION = 4096
FOF_NO_CONNECTED_ELEMENTS = 8192
FOF_WANTNUKEWARNING = 16384
FOF_NORECURSEREPARSE = 32768
FOF_NO_UI = FOF_SILENT | FOF_NOCONFIRMATION | FOF_NOERRORUI | FOF_NOCONFIRMMKDIR

## Extended file operation flags, used with IFileOperation
FOFX_NOSKIPJUNCTIONS = 0x00010000
FOFX_PREFERHARDLINK = 0x00020000
FOFX_SHOWELEVATIONPROMPT = 0x00040000
FOFX_EARLYFAILURE = 0x00100000
FOFX_PRESERVEFILEEXTENSIONS = 0x00200000
FOFX_KEEPNEWERFILE = 0x00400000
FOFX_NOCOPYHOOKS = 0x00800000
FOFX_NOMINIMIZEBOX = 0x01000000
FOFX_MOVEACLSACROSSVOLUMES = 0x02000000
FOFX_DONTDISPLAYSOURCEPATH = 0x04000000
FOFX_DONTDISPLAYDESTPATH = 0x08000000
FOFX_REQUIREELEVATION = 0x10000000
FOFX_COPYASDOWNLOAD = 0x40000000
FOFX_DONTDISPLAYLOCATIONS = 0x80000000

PO_DELETE = 19
PO_RENAME = 20
PO_PORTCHANGE = 32
PO_REN_PORT = 52
SE_ERR_FNF = 2
SE_ERR_PNF = 3
SE_ERR_ACCESSDENIED = 5
SE_ERR_OOM = 8
SE_ERR_DLLNOTFOUND = 32
SE_ERR_SHARE = 26
SE_ERR_ASSOCINCOMPLETE = 27
SE_ERR_DDETIMEOUT = 28
SE_ERR_DDEFAIL = 29
SE_ERR_DDEBUSY = 30
SE_ERR_NOASSOC = 31
SEE_MASK_CLASSNAME = 1
SEE_MASK_CLASSKEY = 3
SEE_MASK_IDLIST = 4
SEE_MASK_INVOKEIDLIST = 12
SEE_MASK_ICON = 16
SEE_MASK_HOTKEY = 32
SEE_MASK_NOCLOSEPROCESS = 64
SEE_MASK_CONNECTNETDRV = 128
SEE_MASK_FLAG_DDEWAIT = 256
SEE_MASK_DOENVSUBST = 512
SEE_MASK_FLAG_NO_UI = 1024
SEE_MASK_UNICODE = 16384
SEE_MASK_NO_CONSOLE = 32768
SEE_MASK_ASYNCOK = 1048576
SEE_MASK_HMONITOR = 2097152
SHERB_NOCONFIRMATION = 1
SHERB_NOPROGRESSUI = 2
SHERB_NOSOUND = 4
NIM_ADD = 0
NIM_MODIFY = 1
NIM_DELETE = 2
NIF_MESSAGE = 1
NIF_ICON = 2
NIF_TIP = 4
SHGFI_ICON = 256
SHGFI_DISPLAYNAME = 512
SHGFI_TYPENAME = 1024
SHGFI_ATTRIBUTES = 2048
SHGFI_ICONLOCATION = 4096
SHGFI_EXETYPE = 8192
SHGFI_SYSICONINDEX = 16384
SHGFI_LINKOVERLAY = 32768
SHGFI_SELECTED = 65536
SHGFI_ATTR_SPECIFIED = 131072
SHGFI_LARGEICON = 0
SHGFI_SMALLICON = 1
SHGFI_OPENICON = 2
SHGFI_SHELLICONSIZE = 4
SHGFI_PIDL = 8
SHGFI_USEFILEATTRIBUTES = 16
SHGNLI_PIDL = 1
SHGNLI_PREFIXNAME = 2
SHGNLI_NOUNIQUE = 4
PRINTACTION_OPEN = 0
PRINTACTION_PROPERTIES = 1
PRINTACTION_NETINSTALL = 2
PRINTACTION_NETINSTALLLINK = 3
PRINTACTION_TESTPAGE = 4
PRINTACTION_OPENNETPRN = 5
PRINTACTION_DOCUMENTDEFAULTS = 6
PRINTACTION_SERVERPROPERTIES = 7

# Flags used with IContextMenu.QueryContextMenu
CMF_NORMAL = 0
CMF_DEFAULTONLY = 1
CMF_VERBSONLY = 2
CMF_EXPLORE = 4
CMF_NOVERBS = 8
CMF_CANRENAME = 16
CMF_NODEFAULT = 32
CMF_INCLUDESTATIC = 64
CMF_ITEMMENU = 128
CMF_EXTENDEDVERBS = 256
CMF_DISABLEDVERBS = 512
CMF_ASYNCVERBSTATE = 1024
CMF_OPTIMIZEFORINVOKE = 2048
CMF_SYNCCASCADEMENU = 4096
CMF_DONOTPICKDEFAULT = 8192
CMF_RESERVED = 0xFFFF0000

GCS_VERBA = 0
GCS_HELPTEXTA = 1
GCS_VALIDATEA = 2
GCS_VERBW = 4
GCS_HELPTEXTW = 5
GCS_VALIDATEW = 6
GCS_UNICODE = 4
GCS_VERB = GCS_VERBA
GCS_HELPTEXT = GCS_HELPTEXTA
GCS_VALIDATE = GCS_VALIDATEA
CMDSTR_NEWFOLDERA = "NewFolder"
CMDSTR_VIEWLISTA = "ViewList"
CMDSTR_VIEWDETAILSA = "ViewDetails"
CMDSTR_NEWFOLDER = CMDSTR_NEWFOLDERA
CMDSTR_VIEWLIST = CMDSTR_VIEWLISTA
CMDSTR_VIEWDETAILS = CMDSTR_VIEWDETAILSA
CMIC_MASK_HOTKEY = SEE_MASK_HOTKEY
CMIC_MASK_ICON = SEE_MASK_ICON
CMIC_MASK_FLAG_NO_UI = SEE_MASK_FLAG_NO_UI
CMIC_MASK_UNICODE = SEE_MASK_UNICODE
CMIC_MASK_NO_CONSOLE = SEE_MASK_NO_CONSOLE
CMIC_MASK_ASYNCOK = SEE_MASK_ASYNCOK
CMIC_MASK_PTINVOKE = 536870912
GIL_OPENICON = 1
GIL_FORSHELL = 2
GIL_ASYNC = 32
GIL_DEFAULTICON = 64
GIL_FORSHORTCUT = 128
GIL_CHECKSHIELD = 512
GIL_SIMULATEDOC = 1
GIL_PERINSTANCE = 2
GIL_PERCLASS = 4
GIL_NOTFILENAME = 8
GIL_DONTCACHE = 16
GIL_SHIELD = 512
GIL_FORCENOSHIELD = 1024
ISIOI_ICONFILE = 1
ISIOI_ICONINDEX = 2
ISIOI_SYSIMAGELISTINDEX = 4
FVSIF_RECT = 1
FVSIF_PINNED = 2
FVSIF_NEWFAILED = 134217728
FVSIF_NEWFILE = -2147483648
FVSIF_CANVIEWIT = 1073741824
FCIDM_SHVIEWFIRST = 0
FCIDM_SHVIEWLAST = 32767
FCIDM_BROWSERFIRST = 40960
FCIDM_BROWSERLAST = 48896
FCIDM_GLOBALFIRST = 32768
FCIDM_GLOBALLAST = 40959
FCIDM_MENU_FILE = FCIDM_GLOBALFIRST + 0
FCIDM_MENU_EDIT = FCIDM_GLOBALFIRST + 64
FCIDM_MENU_VIEW = FCIDM_GLOBALFIRST + 128
FCIDM_MENU_VIEW_SEP_OPTIONS = FCIDM_GLOBALFIRST + 129
FCIDM_MENU_TOOLS = FCIDM_GLOBALFIRST + 192
FCIDM_MENU_TOOLS_SEP_GOTO = FCIDM_GLOBALFIRST + 193
FCIDM_MENU_HELP = FCIDM_GLOBALFIRST + 256
FCIDM_MENU_FIND = FCIDM_GLOBALFIRST + 320
FCIDM_MENU_EXPLORE = FCIDM_GLOBALFIRST + 336
FCIDM_MENU_FAVORITES = FCIDM_GLOBALFIRST + 368
FCIDM_TOOLBAR = FCIDM_BROWSERFIRST + 0
FCIDM_STATUS = FCIDM_BROWSERFIRST + 1
IDC_OFFLINE_HAND = 103
SBSP_DEFBROWSER = 0
SBSP_SAMEBROWSER = 1
SBSP_NEWBROWSER = 2
SBSP_DEFMODE = 0
SBSP_OPENMODE = 16
SBSP_EXPLOREMODE = 32
SBSP_ABSOLUTE = 0
SBSP_RELATIVE = 4096
SBSP_PARENT = 8192
SBSP_NAVIGATEBACK = 16384
SBSP_NAVIGATEFORWARD = 32768
SBSP_ALLOW_AUTONAVIGATE = 65536
SBSP_INITIATEDBYHLINKFRAME = -2147483648
SBSP_REDIRECT = 1073741824
SBSP_WRITENOHISTORY = 134217728
SBSP_NOAUTOSELECT = 67108864
FCW_STATUS = 1
FCW_TOOLBAR = 2
FCW_TREE = 3
FCW_INTERNETBAR = 6
FCW_PROGRESS = 8
FCT_MERGE = 1
FCT_CONFIGABLE = 2
FCT_ADDTOEND = 4
CDBOSC_SETFOCUS = 0
CDBOSC_KILLFOCUS = 1
CDBOSC_SELCHANGE = 2
CDBOSC_RENAME = 3
SVSI_DESELECT = 0
SVSI_SELECT = 1
SVSI_EDIT = 3
SVSI_DESELECTOTHERS = 4
SVSI_ENSUREVISIBLE = 8
SVSI_FOCUSED = 16
SVSI_TRANSLATEPT = 32
SVSI_SELECTIONMARK = 64
SVSI_POSITIONITEM = 128
SVSI_CHECK = 256
SVSI_CHECK2 = 512
SVSI_KEYBOARDSELECT = 1025
SVSI_NOTAKEFOCUS = 1073741824
SVGIO_BACKGROUND = 0
SVGIO_SELECTION = 1
SVGIO_ALLVIEW = 2
SVGIO_CHECKED = (0x3,)
SVGIO_TYPE_MASK = (0xF,)
SVGIO_FLAG_VIEWORDER = -2147483648  # 0x80000000
STRRET_WSTR = 0
STRRET_OFFSET = 1
STRRET_CSTR = 2
CSIDL_DESKTOP = 0
CSIDL_INTERNET = 1
CSIDL_PROGRAMS = 2
CSIDL_CONTROLS = 3
CSIDL_PRINTERS = 4
CSIDL_PERSONAL = 5
CSIDL_FAVORITES = 6
CSIDL_STARTUP = 7
CSIDL_RECENT = 8
CSIDL_SENDTO = 9
CSIDL_BITBUCKET = 10
CSIDL_STARTMENU = 11
CSIDL_MYDOCUMENTS = 12
CSIDL_MYMUSIC = 13
CSIDL_MYVIDEO = 14
CSIDL_DESKTOPDIRECTORY = 16
CSIDL_DRIVES = 17
CSIDL_NETWORK = 18
CSIDL_NETHOOD = 19
CSIDL_FONTS = 20
CSIDL_TEMPLATES = 21
CSIDL_COMMON_STARTMENU = 22
CSIDL_COMMON_PROGRAMS = 23
CSIDL_COMMON_STARTUP = 24
CSIDL_COMMON_DESKTOPDIRECTORY = 25
CSIDL_APPDATA = 26
CSIDL_PRINTHOOD = 27
CSIDL_LOCAL_APPDATA = 28
CSIDL_ALTSTARTUP = 29
CSIDL_COMMON_ALTSTARTUP = 30
CSIDL_COMMON_FAVORITES = 31
CSIDL_INTERNET_CACHE = 32
CSIDL_COOKIES = 33
CSIDL_HISTORY = 34
CSIDL_COMMON_APPDATA = 35
CSIDL_WINDOWS = 36
CSIDL_SYSTEM = 37
CSIDL_PROGRAM_FILES = 38
CSIDL_MYPICTURES = 39
CSIDL_PROFILE = 40
CSIDL_SYSTEMX86 = 41
CSIDL_PROGRAM_FILESX86 = 42
CSIDL_PROGRAM_FILES_COMMON = 43
CSIDL_PROGRAM_FILES_COMMONX86 = 44
CSIDL_COMMON_TEMPLATES = 45
CSIDL_COMMON_DOCUMENTS = 46
CSIDL_COMMON_ADMINTOOLS = 47
CSIDL_ADMINTOOLS = 48
CSIDL_CONNECTIONS = 49
CSIDL_COMMON_MUSIC = 53
CSIDL_COMMON_PICTURES = 54
CSIDL_COMMON_VIDEO = 55
CSIDL_RESOURCES = 56
CSIDL_RESOURCES_LOCALIZED = 57
CSIDL_COMMON_OEM_LINKS = 58
CSIDL_CDBURN_AREA = 59
# 60 unused
CSIDL_COMPUTERSNEARME = 61

BIF_RETURNONLYFSDIRS = 1
BIF_DONTGOBELOWDOMAIN = 2
BIF_STATUSTEXT = 4
BIF_RETURNFSANCESTORS = 8
BIF_EDITBOX = 16
BIF_VALIDATE = 32
BIF_BROWSEFORCOMPUTER = 4096
BIF_BROWSEFORPRINTER = 8192
BIF_BROWSEINCLUDEFILES = 16384
BFFM_INITIALIZED = 1
BFFM_SELCHANGED = 2
BFFM_VALIDATEFAILEDA = 3
BFFM_VALIDATEFAILEDW = 4
BFFM_SETSTATUSTEXTA = WM_USER + 100
BFFM_ENABLEOK = WM_USER + 101
BFFM_SETSELECTIONA = WM_USER + 102
BFFM_SETSELECTIONW = WM_USER + 103
BFFM_SETSTATUSTEXTW = WM_USER + 104
BFFM_SETSTATUSTEXT = BFFM_SETSTATUSTEXTA
BFFM_SETSELECTION = BFFM_SETSELECTIONA
BFFM_VALIDATEFAILED = BFFM_VALIDATEFAILEDA
SFGAO_CANCOPY = DROPEFFECT_COPY
SFGAO_CANMOVE = DROPEFFECT_MOVE
SFGAO_CANLINK = DROPEFFECT_LINK
SFGAO_CANRENAME = 16
SFGAO_CANDELETE = 32
SFGAO_HASPROPSHEET = 64
SFGAO_DROPTARGET = 256
SFGAO_CAPABILITYMASK = 375
SFGAO_LINK = 65536
SFGAO_SHARE = 131072
SFGAO_READONLY = 262144
SFGAO_GHOSTED = 524288
SFGAO_HIDDEN = 524288
SFGAO_DISPLAYATTRMASK = 983040
SFGAO_FILESYSANCESTOR = *********
SFGAO_FOLDER = 536870912
SFGAO_FILESYSTEM = 1073741824
SFGAO_HASSUBFOLDER = -2147483648
SFGAO_CONTENTSMASK = -2147483648
SFGAO_VALIDATE = 16777216
SFGAO_REMOVABLE = 33554432
SFGAO_COMPRESSED = 67108864
SFGAO_BROWSABLE = 134217728
SFGAO_NONENUMERATED = 1048576
SFGAO_NEWCONTENT = 2097152
SFGAO_STORAGE = 8
DWFRF_NORMAL = 0
DWFRF_DELETECONFIGDATA = 1
DWFAF_HIDDEN = 1
CFSTR_SHELLIDLIST = "Shell IDList Array"
CFSTR_SHELLIDLISTOFFSET = "Shell Object Offsets"
CFSTR_NETRESOURCES = "Net Resource"
CFSTR_FILEDESCRIPTORA = "FileGroupDescriptor"
CFSTR_FILEDESCRIPTORW = "FileGroupDescriptorW"
CFSTR_FILECONTENTS = "FileContents"
CFSTR_FILENAMEA = "FileName"
CFSTR_FILENAMEW = "FileNameW"
CFSTR_PRINTERGROUP = "PrinterFriendlyName"
CFSTR_FILENAMEMAPA = "FileNameMap"
CFSTR_FILENAMEMAPW = "FileNameMapW"
CFSTR_SHELLURL = "UniformResourceLocator"
CFSTR_INETURLA = CFSTR_SHELLURL
CFSTR_INETURLW = "UniformResourceLocatorW"
CFSTR_PREFERREDDROPEFFECT = "Preferred DropEffect"
CFSTR_PERFORMEDDROPEFFECT = "Performed DropEffect"
CFSTR_PASTESUCCEEDED = "Paste Succeeded"
CFSTR_INDRAGLOOP = "InShellDragLoop"
CFSTR_DRAGCONTEXT = "DragContext"
CFSTR_MOUNTEDVOLUME = "MountedVolume"
CFSTR_PERSISTEDDATAOBJECT = "PersistedDataObject"
CFSTR_TARGETCLSID = "TargetCLSID"
CFSTR_LOGICALPERFORMEDDROPEFFECT = "Logical Performed DropEffect"
CFSTR_AUTOPLAY_SHELLIDLISTS = "Autoplay Enumerated IDList Array"
CFSTR_FILEDESCRIPTOR = CFSTR_FILEDESCRIPTORA
CFSTR_FILENAME = CFSTR_FILENAMEA
CFSTR_FILENAMEMAP = CFSTR_FILENAMEMAPA
DVASPECT_SHORTNAME = 2
SHCNE_RENAMEITEM = 1
SHCNE_CREATE = 2
SHCNE_DELETE = 4
SHCNE_MKDIR = 8
SHCNE_RMDIR = 16
SHCNE_MEDIAINSERTED = 32
SHCNE_MEDIAREMOVED = 64
SHCNE_DRIVEREMOVED = 128
SHCNE_DRIVEADD = 256
SHCNE_NETSHARE = 512
SHCNE_NETUNSHARE = 1024
SHCNE_ATTRIBUTES = 2048
SHCNE_UPDATEDIR = 4096
SHCNE_UPDATEITEM = 8192
SHCNE_SERVERDISCONNECT = 16384
SHCNE_UPDATEIMAGE = 32768
SHCNE_DRIVEADDGUI = 65536
SHCNE_RENAMEFOLDER = 131072
SHCNE_FREESPACE = 262144
SHCNE_EXTENDED_EVENT = 67108864
SHCNE_ASSOCCHANGED = 134217728
SHCNE_DISKEVENTS = 145439
SHCNE_GLOBALEVENTS = 201687520
SHCNE_ALLEVENTS = 2147483647
SHCNE_INTERRUPT = -2147483648
SHCNEE_ORDERCHANGED = 2
SHCNF_IDLIST = 0
SHCNF_PATHA = 1
SHCNF_PRINTERA = 2
SHCNF_DWORD = 3
SHCNF_PATHW = 5
SHCNF_PRINTERW = 6
SHCNF_TYPE = 255
SHCNF_FLUSH = 4096
SHCNF_FLUSHNOWAIT = 8192
SHCNF_PATH = SHCNF_PATHA
SHCNF_PRINTER = SHCNF_PRINTERA
QIF_CACHED = 1
QIF_DONTEXPANDFOLDER = 2

# ShellWindowFindWindowOptions
SWFO_NEEDDISPATCH = 1
SWFO_INCLUDEPENDING = 2
SWFO_COOKIEPASSED = 4

# ShellWindowTypeConstants
SWC_EXPLORER = 0
SWC_BROWSER = 1
SWC_3RDPARTY = 2
SWC_CALLBACK = 4
SWC_DESKTOP = 8

# SHARD enum for SHAddToRecentDocs
SHARD_PIDL = 1
SHARD_PATHA = 2
SHARD_PATHW = 3
SHARD_APPIDINFO = 4
SHARD_APPIDINFOIDLIST = 5
SHARD_LINK = 6
SHARD_APPIDINFOLINK = 7
SHARD_SHELLITEM = 8
## SHARD_PATH = SHARD_PATHW
SHARD_PATH = SHARD_PATHA

SHGDFIL_FINDDATA = 1
SHGDFIL_NETRESOURCE = 2
SHGDFIL_DESCRIPTIONID = 3
SHDID_ROOT_REGITEM = 1
SHDID_FS_FILE = 2
SHDID_FS_DIRECTORY = 3
SHDID_FS_OTHER = 4
SHDID_COMPUTER_DRIVE35 = 5
SHDID_COMPUTER_DRIVE525 = 6
SHDID_COMPUTER_REMOVABLE = 7
SHDID_COMPUTER_FIXED = 8
SHDID_COMPUTER_NETDRIVE = 9
SHDID_COMPUTER_CDROM = 10
SHDID_COMPUTER_RAMDISK = 11
SHDID_COMPUTER_OTHER = 12
SHDID_NET_DOMAIN = 13
SHDID_NET_SERVER = 14
SHDID_NET_SHARE = 15
SHDID_NET_RESTOFNET = 16
SHDID_NET_OTHER = 17
PID_IS_URL = 2
PID_IS_NAME = 4
PID_IS_WORKINGDIR = 5
PID_IS_HOTKEY = 6
PID_IS_SHOWCMD = 7
PID_IS_ICONINDEX = 8
PID_IS_ICONFILE = 9
PID_IS_WHATSNEW = 10
PID_IS_AUTHOR = 11
PID_IS_DESCRIPTION = 12
PID_IS_COMMENT = 13
PID_INTSITE_WHATSNEW = 2
PID_INTSITE_AUTHOR = 3
PID_INTSITE_LASTVISIT = 4
PID_INTSITE_LASTMOD = 5
PID_INTSITE_VISITCOUNT = 6
PID_INTSITE_DESCRIPTION = 7
PID_INTSITE_COMMENT = 8
PID_INTSITE_FLAGS = 9
PID_INTSITE_CONTENTLEN = 10
PID_INTSITE_CONTENTCODE = 11
PID_INTSITE_RECURSE = 12
PID_INTSITE_WATCH = 13
PID_INTSITE_SUBSCRIPTION = 14
PID_INTSITE_URL = 15
PID_INTSITE_TITLE = 16
PID_INTSITE_CODEPAGE = 18
PID_INTSITE_TRACKING = 19
PIDISF_RECENTLYCHANGED = 1
PIDISF_CACHEDSTICKY = 2
PIDISF_CACHEIMAGES = 16
PIDISF_FOLLOWALLLINKS = 32
PIDISM_GLOBAL = 0
PIDISM_WATCH = 1
PIDISM_DONTWATCH = 2
SSF_SHOWALLOBJECTS = 1
SSF_SHOWEXTENSIONS = 2
SSF_SHOWCOMPCOLOR = 8
SSF_SHOWSYSFILES = 32
SSF_DOUBLECLICKINWEBVIEW = 128
SSF_SHOWATTRIBCOL = 256
SSF_DESKTOPHTML = 512
SSF_WIN95CLASSIC = 1024
SSF_DONTPRETTYPATH = 2048
SSF_SHOWINFOTIP = 8192
SSF_MAPNETDRVBUTTON = 4096
SSF_NOCONFIRMRECYCLE = 32768
SSF_HIDEICONS = 16384

ABM_NEW = 0
ABM_REMOVE = 1
ABM_QUERYPOS = 2
ABM_SETPOS = 3
ABM_GETSTATE = 4
ABM_GETTASKBARPOS = 5
ABM_ACTIVATE = 6
ABM_GETAUTOHIDEBAR = 7
ABM_SETAUTOHIDEBAR = 8
ABM_WINDOWPOSCHANGED = 9
ABN_STATECHANGE = 0
ABN_POSCHANGED = 1
ABN_FULLSCREENAPP = 2
ABN_WINDOWARRANGE = 3
ABS_AUTOHIDE = 1
ABS_ALWAYSONTOP = 2
ABE_LEFT = 0
ABE_TOP = 1
ABE_RIGHT = 2
ABE_BOTTOM = 3


def EIRESID(x):
    return -1 * (int)(x)


# Some manually added ones

SHCONTF_FOLDERS = 32  # for shell browser
SHCONTF_NONFOLDERS = 64  # for default view
SHCONTF_INCLUDEHIDDEN = 128  # for hidden/system objects
SHCONTF_INIT_ON_FIRST_NEXT = 256
SHCONTF_NETPRINTERSRCH = 512
SHCONTF_SHAREABLE = 1024
SHCONTF_STORAGE = 2048

SHGDN_NORMAL = 0  # default (display purpose)
SHGDN_INFOLDER = 1  # displayed under a folder (relative)
SHGDN_FOREDITING = 4096  # for in-place editing
SHGDN_INCLUDE_NONFILESYS = 8192  # if not set, display names for shell name space items that are not in the file system will fail.
SHGDN_FORADDRESSBAR = 16384  # for displaying in the address (drives dropdown) bar
SHGDN_FORPARSING = 32768  # for ParseDisplayName or path

BFO_NONE = 0
BFO_BROWSER_PERSIST_SETTINGS = 1
BFO_RENAME_FOLDER_OPTIONS_TOINTERNET = 2
BFO_BOTH_OPTIONS = 4
BIF_PREFER_INTERNET_SHORTCUT = 8
BFO_BROWSE_NO_IN_NEW_PROCESS = 16
BFO_ENABLE_HYPERLINK_TRACKING = 32
BFO_USE_IE_OFFLINE_SUPPORT = 64
BFO_SUBSTITUE_INTERNET_START_PAGE = 128
BFO_USE_IE_LOGOBANDING = 256
BFO_ADD_IE_TOCAPTIONBAR = 512
BFO_USE_DIALUP_REF = 1024
BFO_USE_IE_TOOLBAR = 2048
BFO_NO_PARENT_FOLDER_SUPPORT = 4096
BFO_NO_REOPEN_NEXT_RESTART = 8192
BFO_GO_HOME_PAGE = 16384
BFO_PREFER_IEPROCESS = 32768
BFO_SHOW_NAVIGATION_CANCELLED = 65536
BFO_QUERY_ALL = -1
# From ShlGuid.h
PID_FINDDATA = 0
PID_NETRESOURCE = 1
PID_DESCRIPTIONID = 2
PID_WHICHFOLDER = 3
PID_NETWORKLOCATION = 4
PID_COMPUTERNAME = 5
PID_DISPLACED_FROM = 2
PID_DISPLACED_DATE = 3
PID_SYNC_COPY_IN = 2
PID_MISC_STATUS = 2
PID_MISC_ACCESSCOUNT = 3
PID_MISC_OWNER = 4
PID_HTMLINFOTIPFILE = 5
PID_MISC_PICS = 6
PID_DISPLAY_PROPERTIES = 0
PID_INTROTEXT = 1
PIDSI_ARTIST = 2
PIDSI_SONGTITLE = 3
PIDSI_ALBUM = 4
PIDSI_YEAR = 5
PIDSI_COMMENT = 6
PIDSI_TRACK = 7
PIDSI_GENRE = 11
PIDSI_LYRICS = 12
PIDDRSI_PROTECTED = 2
PIDDRSI_DESCRIPTION = 3
PIDDRSI_PLAYCOUNT = 4
PIDDRSI_PLAYSTARTS = 5
PIDDRSI_PLAYEXPIRES = 6
PIDVSI_STREAM_NAME = 2
PIDVSI_FRAME_WIDTH = 3
PIDVSI_FRAME_HEIGHT = 4
PIDVSI_TIMELENGTH = 7
PIDVSI_FRAME_COUNT = 5
PIDVSI_FRAME_RATE = 6
PIDVSI_DATA_RATE = 8
PIDVSI_SAMPLE_SIZE = 9
PIDVSI_COMPRESSION = 10
PIDVSI_STREAM_NUMBER = 11
PIDASI_FORMAT = 2
PIDASI_TIMELENGTH = 3
PIDASI_AVG_DATA_RATE = 4
PIDASI_SAMPLE_RATE = 5
PIDASI_SAMPLE_SIZE = 6
PIDASI_CHANNEL_COUNT = 7
PIDASI_STREAM_NUMBER = 8
PIDASI_STREAM_NAME = 9
PIDASI_COMPRESSION = 10
PID_CONTROLPANEL_CATEGORY = 2
PID_VOLUME_FREE = 2
PID_VOLUME_CAPACITY = 3
PID_VOLUME_FILESYSTEM = 4
PID_SHARE_CSC_STATUS = 2
PID_LINK_TARGET = 2
PID_QUERY_RANK = 2
# From PropIdl.h
PROPSETFLAG_DEFAULT = 0
PROPSETFLAG_NONSIMPLE = 1
PROPSETFLAG_ANSI = 2
PROPSETFLAG_UNBUFFERED = 4
PROPSETFLAG_CASE_SENSITIVE = 8
PROPSET_BEHAVIOR_CASE_SENSITIVE = 1
PID_DICTIONARY = 0
PID_CODEPAGE = 1
PID_FIRST_USABLE = 2
PID_FIRST_NAME_DEFAULT = 4095
PID_LOCALE = -2147483648
PID_MODIFY_TIME = -2147483647
PID_SECURITY = -2147483646
PID_BEHAVIOR = -2147483645
PID_ILLEGAL = -1
PID_MIN_READONLY = -2147483648
PID_MAX_READONLY = -1073741825
PIDDI_THUMBNAIL = 2
PIDSI_TITLE = 2
PIDSI_SUBJECT = 3
PIDSI_AUTHOR = 4
PIDSI_KEYWORDS = 5
PIDSI_COMMENTS = 6
PIDSI_TEMPLATE = 7
PIDSI_LASTAUTHOR = 8
PIDSI_REVNUMBER = 9
PIDSI_EDITTIME = 10
PIDSI_LASTPRINTED = 11
PIDSI_CREATE_DTM = 12
PIDSI_LASTSAVE_DTM = 13
PIDSI_PAGECOUNT = 14
PIDSI_WORDCOUNT = 15
PIDSI_CHARCOUNT = 16
PIDSI_THUMBNAIL = 17
PIDSI_APPNAME = 18
PIDSI_DOC_SECURITY = 19
PIDDSI_CATEGORY = 2
PIDDSI_PRESFORMAT = 3
PIDDSI_BYTECOUNT = 4
PIDDSI_LINECOUNT = 5
PIDDSI_PARCOUNT = 6
PIDDSI_SLIDECOUNT = 7
PIDDSI_NOTECOUNT = 8
PIDDSI_HIDDENCOUNT = 9
PIDDSI_MMCLIPCOUNT = 10
PIDDSI_SCALE = 11
PIDDSI_HEADINGPAIR = 12
PIDDSI_DOCPARTS = 13
PIDDSI_MANAGER = 14
PIDDSI_COMPANY = 15
PIDDSI_LINKSDIRTY = 16
PIDMSI_EDITOR = 2
PIDMSI_SUPPLIER = 3
PIDMSI_SOURCE = 4
PIDMSI_SEQUENCE_NO = 5
PIDMSI_PROJECT = 6
PIDMSI_STATUS = 7
PIDMSI_OWNER = 8
PIDMSI_RATING = 9
PIDMSI_PRODUCTION = 10
PIDMSI_COPYRIGHT = 11
PRSPEC_INVALID = -1
PRSPEC_LPWSTR = 0
PRSPEC_PROPID = 1
# From ShObjIdl.h
SHCIDS_ALLFIELDS = -2147483648
SHCIDS_CANONICALONLY = *********
SHCIDS_BITMASK = -65536
SHCIDS_COLUMNMASK = 65535
SFGAO_CANMONIKER = 4194304
SFGAO_HASSTORAGE = 4194304
SFGAO_STREAM = 4194304
SFGAO_STORAGEANCESTOR = 8388608
SFGAO_STORAGECAPMASK = 1891958792

MAXPROPPAGES = 100
PSP_DEFAULT = 0
PSP_DLGINDIRECT = 1
PSP_USEHICON = 2
PSP_USEICONID = 4
PSP_USETITLE = 8
PSP_RTLREADING = 16
PSP_HASHELP = 32
PSP_USEREFPARENT = 64
PSP_USECALLBACK = 128
PSP_PREMATURE = 1024
PSP_HIDEHEADER = 2048
PSP_USEHEADERTITLE = 4096
PSP_USEHEADERSUBTITLE = 8192
PSP_USEFUSIONCONTEXT = 16384
PSPCB_ADDREF = 0
PSPCB_RELEASE = 1
PSPCB_CREATE = 2

PSH_DEFAULT = 0
PSH_PROPTITLE = 1
PSH_USEHICON = 2
PSH_USEICONID = 4
PSH_PROPSHEETPAGE = 8
PSH_WIZARDHASFINISH = 16
PSH_WIZARD = 32
PSH_USEPSTARTPAGE = 64
PSH_NOAPPLYNOW = 128
PSH_USECALLBACK = 256
PSH_HASHELP = 512
PSH_MODELESS = 1024
PSH_RTLREADING = 2048
PSH_WIZARDCONTEXTHELP = 4096
PSH_WIZARD97 = 16777216
PSH_WATERMARK = 32768
PSH_USEHBMWATERMARK = 65536
PSH_USEHPLWATERMARK = 131072
PSH_STRETCHWATERMARK = 262144
PSH_HEADER = 524288
PSH_USEHBMHEADER = 1048576
PSH_USEPAGELANG = 2097152
PSH_WIZARD_LITE = 4194304
PSH_NOCONTEXTHELP = 33554432

PSCB_INITIALIZED = 1
PSCB_PRECREATE = 2
PSCB_BUTTONPRESSED = 3
PSNRET_NOERROR = 0
PSNRET_INVALID = 1
PSNRET_INVALID_NOCHANGEPAGE = 2
PSNRET_MESSAGEHANDLED = 3

PSWIZB_BACK = 1
PSWIZB_NEXT = 2
PSWIZB_FINISH = 4
PSWIZB_DISABLEDFINISH = 8
PSBTN_BACK = 0
PSBTN_NEXT = 1
PSBTN_FINISH = 2
PSBTN_OK = 3
PSBTN_APPLYNOW = 4
PSBTN_CANCEL = 5
PSBTN_HELP = 6
PSBTN_MAX = 6

ID_PSRESTARTWINDOWS = 2
ID_PSREBOOTSYSTEM = ID_PSRESTARTWINDOWS | 1
WIZ_CXDLG = 276
WIZ_CYDLG = 140
WIZ_CXBMP = 80
WIZ_BODYX = 92
WIZ_BODYCX = 184
PROP_SM_CXDLG = 212
PROP_SM_CYDLG = 188
PROP_MED_CXDLG = 227
PROP_MED_CYDLG = 215
PROP_LG_CXDLG = 252
PROP_LG_CYDLG = 218
ISOLATION_AWARE_USE_STATIC_LIBRARY = 0
ISOLATION_AWARE_BUILD_STATIC_LIBRARY = 0

SHCOLSTATE_TYPE_STR = 1
SHCOLSTATE_TYPE_INT = 2
SHCOLSTATE_TYPE_DATE = 3
SHCOLSTATE_TYPEMASK = 15
SHCOLSTATE_ONBYDEFAULT = 16
SHCOLSTATE_SLOW = 32
SHCOLSTATE_EXTENDED = 64
SHCOLSTATE_SECONDARYUI = 128
SHCOLSTATE_HIDDEN = 256
SHCOLSTATE_PREFER_VARCMP = 512

FWF_AUTOARRANGE = 1
FWF_ABBREVIATEDNAMES = 2
FWF_SNAPTOGRID = 4
FWF_OWNERDATA = 8
FWF_BESTFITWINDOW = 16
FWF_DESKTOP = 32
FWF_SINGLESEL = 64
FWF_NOSUBFOLDERS = 128
FWF_TRANSPARENT = 256
FWF_NOCLIENTEDGE = 512
FWF_NOSCROLL = 1024
FWF_ALIGNLEFT = 2048
FWF_NOICONS = 4096
FWF_SHOWSELALWAYS = 8192
FWF_NOVISIBLE = 16384
FWF_SINGLECLICKACTIVATE = 32768
FWF_NOWEBVIEW = 65536
FWF_HIDEFILENAMES = 131072
FWF_CHECKSELECT = 262144

FVM_FIRST = 1
FVM_ICON = 1
FVM_SMALLICON = 2
FVM_LIST = 3
FVM_DETAILS = 4
FVM_THUMBNAIL = 5
FVM_TILE = 6
FVM_THUMBSTRIP = 7

SVUIA_DEACTIVATE = 0
SVUIA_ACTIVATE_NOFOCUS = 1
SVUIA_ACTIVATE_FOCUS = 2
SVUIA_INPLACEACTIVATE = 3

# SHChangeNotifyRegister flags
SHCNRF_InterruptLevel = 1
SHCNRF_ShellLevel = 2
SHCNRF_RecursiveInterrupt = 4096
SHCNRF_NewDelivery = 32768

FD_CLSID = 0x0001
FD_SIZEPOINT = 0x0002
FD_ATTRIBUTES = 0x0004
FD_CREATETIME = 0x0008
FD_ACCESSTIME = 0x0010
FD_WRITESTIME = 0x0020
FD_FILESIZE = 0x0040
FD_PROGRESSUI = 0x4000
FD_LINKUI = 0x8000

# shlwapi stuff
ASSOCF_INIT_NOREMAPCLSID = 0x00000001  #  do not remap clsids to progids
ASSOCF_INIT_BYEXENAME = 0x00000002  # executable is being passed in
ASSOCF_OPEN_BYEXENAME = 0x00000002  # executable is being passed in
ASSOCF_INIT_DEFAULTTOSTAR = 0x00000004  # treat "*" as the BaseClass
ASSOCF_INIT_DEFAULTTOFOLDER = 0x00000008  # treat "Folder" as the BaseClass
ASSOCF_NOUSERSETTINGS = 0x00000010  #  don't use HKCU
ASSOCF_NOTRUNCATE = 0x00000020  # don't truncate the return string
ASSOCF_VERIFY = 0x00000040  #  verify data is accurate (DISK HITS)
ASSOCF_REMAPRUNDLL = 0x00000080  # actually gets info about rundlls target if applicable
ASSOCF_NOFIXUPS = 0x00000100  # attempt to fix errors if found
ASSOCF_IGNOREBASECLASS = 0x00000200  # don't recurse into the baseclass

ASSOCSTR_COMMAND = 1  # shell\verb\command string
ASSOCSTR_EXECUTABLE = 2  # the executable part of command string
ASSOCSTR_FRIENDLYDOCNAME = 3  # friendly name of the document type
ASSOCSTR_FRIENDLYAPPNAME = 4  # friendly name of executable
ASSOCSTR_NOOPEN = 5  # noopen value
ASSOCSTR_SHELLNEWVALUE = 6  # query values under the shellnew key
ASSOCSTR_DDECOMMAND = 7  # template for DDE commands
ASSOCSTR_DDEIFEXEC = 8  # DDECOMMAND to use if just create a process
ASSOCSTR_DDEAPPLICATION = 9  # Application name in DDE broadcast
ASSOCSTR_DDETOPIC = 10  # Topic Name in DDE broadcast
ASSOCSTR_INFOTIP = (
    11  # info tip for an item, or list of properties to create info tip from
)
ASSOCSTR_QUICKTIP = 12  # same as ASSOCSTR_INFOTIP, except, this list contains only quickly retrievable properties
ASSOCSTR_TILEINFO = (
    13  # similar to ASSOCSTR_INFOTIP - lists important properties for tileview
)
ASSOCSTR_CONTENTTYPE = 14  # MIME Content type
ASSOCSTR_DEFAULTICON = 15  # Default icon source
ASSOCSTR_SHELLEXTENSION = (
    16  # Guid string pointing to the Shellex\Shellextensionhandler value.
)

ASSOCKEY_SHELLEXECCLASS = 1  # the key that should be passed to ShellExec(hkeyClass)
ASSOCKEY_APP = 2  # the "Application" key for the association
ASSOCKEY_CLASS = 3  # the progid or class key
ASSOCKEY_BASECLASS = 4  # the BaseClass key

ASSOCDATA_MSIDESCRIPTOR = 1  # Component Descriptor to pass to MSI APIs
ASSOCDATA_NOACTIVATEHANDLER = 2  # restrict attempts to activate window
ASSOCDATA_QUERYCLASSSTORE = 3  # should check with the NT Class Store
ASSOCDATA_HASPERUSERASSOC = 4  # defaults to user specified association
ASSOCDATA_EDITFLAGS = 5  # Edit flags.
ASSOCDATA_VALUE = 6  # use pszExtra as the Value name

# flags used with SHGetViewStatePropertyBag
SHGVSPB_PERUSER = 1
SHGVSPB_ALLUSERS = 2
SHGVSPB_PERFOLDER = 4
SHGVSPB_ALLFOLDERS = 8
SHGVSPB_INHERIT = 16
SHGVSPB_ROAM = 32
SHGVSPB_NOAUTODEFAULTS = 0x80000000
SHGVSPB_FOLDER = SHGVSPB_PERUSER | SHGVSPB_PERFOLDER
SHGVSPB_FOLDERNODEFAULTS = SHGVSPB_PERUSER | SHGVSPB_PERFOLDER | SHGVSPB_NOAUTODEFAULTS
SHGVSPB_USERDEFAULTS = SHGVSPB_PERUSER | SHGVSPB_ALLFOLDERS
SHGVSPB_GLOBALDEAFAULTS = SHGVSPB_ALLUSERS | SHGVSPB_ALLFOLDERS

# IDeskband and related
DBIM_MINSIZE = 0x0001
DBIM_MAXSIZE = 0x0002
DBIM_INTEGRAL = 0x0004
DBIM_ACTUAL = 0x0008
DBIM_TITLE = 0x0010
DBIM_MODEFLAGS = 0x0020
DBIM_BKCOLOR = 0x0040

DBIMF_NORMAL = 0x0000
DBIMF_VARIABLEHEIGHT = 0x0008
DBIMF_DEBOSSED = 0x0020
DBIMF_BKCOLOR = 0x0040

DBIF_VIEWMODE_NORMAL = 0x0000
DBIF_VIEWMODE_VERTICAL = 0x0001
DBIF_VIEWMODE_FLOATING = 0x0002
DBIF_VIEWMODE_TRANSPARENT = 0x0004

# Message types used with SHShellFolderView_Message
SFVM_REARRANGE = 1
SFVM_ADDOBJECT = 3
SFVM_REMOVEOBJECT = 6
SFVM_UPDATEOBJECT = 7
SFVM_GETSELECTEDOBJECTS = 9
SFVM_SETITEMPOS = 14
SFVM_SETCLIPBOARD = 16
SFVM_SETPOINTS = 23

# SHELL_LINK_DATA_FLAGS enum, used with IShellLinkDatalist
SLDF_HAS_ID_LIST = 1
SLDF_HAS_LINK_INFO = 2
SLDF_HAS_NAME = 4
SLDF_HAS_RELPATH = 8
SLDF_HAS_WORKINGDIR = 16
SLDF_HAS_ARGS = 32
SLDF_HAS_ICONLOCATION = 64
SLDF_UNICODE = 128
SLDF_FORCE_NO_LINKINFO = 256
SLDF_HAS_EXP_SZ = 512
SLDF_RUN_IN_SEPARATE = 1024
SLDF_HAS_LOGO3ID = 2048
SLDF_HAS_DARWINID = 4096
SLDF_RUNAS_USER = 8192
SLDF_NO_PIDL_ALIAS = 32768
SLDF_FORCE_UNCNAME = 65536
SLDF_HAS_EXP_ICON_SZ = 16384
SLDF_RUN_WITH_SHIMLAYER = 131072
SLDF_RESERVED = 2147483648

# IShellLinkDataList data block signatures
EXP_SPECIAL_FOLDER_SIG = *********5
NT_CONSOLE_PROPS_SIG = *********2
NT_FE_CONSOLE_PROPS_SIG = *********4
EXP_DARWIN_ID_SIG = *********6
EXP_LOGO3_ID_SIG = *********7
EXP_SZ_ICON_SIG = *********7
EXP_SZ_LINK_SIG = *********1

# IURL_SETURL_FLAGS enum, used with PyIUniformResourceLocator.SetURL
IURL_SETURL_FL_GUESS_PROTOCOL = 1
IURL_SETURL_FL_USE_DEFAULT_PROTOCOL = 2

# IURL_INVOKECOMMAND_FLAGS enum, used with PyIUniformResourceLocator.InvokeCommand
IURL_INVOKECOMMAND_FL_ALLOW_UI = 1
IURL_INVOKECOMMAND_FL_USE_DEFAULT_VERB = 2
IURL_INVOKECOMMAND_FL_DDEWAIT = 4

## constants used with IActiveDesktop
# COMPONENT.ComponentType
COMP_TYPE_HTMLDOC = 0
COMP_TYPE_PICTURE = 1
COMP_TYPE_WEBSITE = 2
COMP_TYPE_CONTROL = 3
COMP_TYPE_CFHTML = 4
COMP_TYPE_MAX = 4
# COMPONENT.CurItemState
IS_NORMAL = 1
IS_FULLSCREEN = 2
IS_SPLIT = 4
IS_VALIDSIZESTATEBITS = IS_NORMAL | IS_SPLIT | IS_FULLSCREEN
IS_VALIDSTATEBITS = IS_NORMAL | IS_SPLIT | IS_FULLSCREEN | 0x80000000 | 0x40000000
# IActiveDesktop.ApplyChanges Flags
AD_APPLY_SAVE = 1
AD_APPLY_HTMLGEN = 2
AD_APPLY_REFRESH = 4
AD_APPLY_ALL = AD_APPLY_SAVE | AD_APPLY_HTMLGEN | AD_APPLY_REFRESH
AD_APPLY_FORCE = 8
AD_APPLY_BUFFERED_REFRESH = 16
AD_APPLY_DYNAMICREFRESH = 32
# Wallpaper styles used with GetWallpaper and SetWallpaper
WPSTYLE_CENTER = 0
WPSTYLE_TILE = 1
WPSTYLE_STRETCH = 2
WPSTYLE_MAX = 3
# ModifyDesktopItem flags
COMP_ELEM_TYPE = 0x00000001
COMP_ELEM_CHECKED = 0x00000002
COMP_ELEM_DIRTY = 0x00000004
COMP_ELEM_NOSCROLL = 0x00000008
COMP_ELEM_POS_LEFT = 0x00000010
COMP_ELEM_POS_TOP = 0x00000020
COMP_ELEM_SIZE_WIDTH = 0x00000040
COMP_ELEM_SIZE_HEIGHT = 0x00000080
COMP_ELEM_POS_ZINDEX = 0x00000100
COMP_ELEM_SOURCE = 0x00000200
COMP_ELEM_FRIENDLYNAME = 0x00000400
COMP_ELEM_SUBSCRIBEDURL = 0x00000800
COMP_ELEM_ORIGINAL_CSI = 0x00001000
COMP_ELEM_RESTORED_CSI = 0x00002000
COMP_ELEM_CURITEMSTATE = 0x00004000
COMP_ELEM_ALL = (
    COMP_ELEM_TYPE
    | COMP_ELEM_CHECKED
    | COMP_ELEM_DIRTY
    | COMP_ELEM_NOSCROLL
    | COMP_ELEM_POS_LEFT
    | COMP_ELEM_SIZE_WIDTH
    | COMP_ELEM_SIZE_HEIGHT
    | COMP_ELEM_POS_ZINDEX
    | COMP_ELEM_SOURCE
    | COMP_ELEM_FRIENDLYNAME
    | COMP_ELEM_POS_TOP
    | COMP_ELEM_SUBSCRIBEDURL
    | COMP_ELEM_ORIGINAL_CSI
    | COMP_ELEM_RESTORED_CSI
    | COMP_ELEM_CURITEMSTATE
)

DTI_ADDUI_DEFAULT = 0
DTI_ADDUI_DISPSUBWIZARD = 1
DTI_ADDUI_POSITIONITEM = 2
ADDURL_SILENT = 0x0001
COMPONENT_TOP = 0x3FFFFFFF
COMPONENT_DEFAULT_LEFT = 0xFFFF
COMPONENT_DEFAULT_TOP = 0xFFFF

SSM_CLEAR = 0
SSM_SET = 1
SSM_REFRESH = 2
SSM_UPDATE = 4

SCHEME_DISPLAY = 0x0001
SCHEME_EDIT = 0x0002
SCHEME_LOCAL = 0x0004
SCHEME_GLOBAL = 0x0008
SCHEME_REFRESH = 0x0010
SCHEME_UPDATE = 0x0020
SCHEME_DONOTUSE = 0x0040
SCHEME_CREATE = 0x0080

GADOF_DIRTY = 1

# From EmptyVC.h
EVCF_HASSETTINGS = 0x0001
EVCF_ENABLEBYDEFAULT = 0x0002
EVCF_REMOVEFROMLIST = 0x0004
EVCF_ENABLEBYDEFAULT_AUTO = 0x0008
EVCF_DONTSHOWIFZERO = 0x0010
EVCF_SETTINGSMODE = 0x0020
EVCF_OUTOFDISKSPACE = 0x0040
EVCCBF_LASTNOTIFICATION = 0x0001

# ShObjIdl.h IExplorer* related
EBO_NONE = 0
EBO_NAVIGATEONCE = 0x1
EBO_SHOWFRAMES = 0x2
EBO_ALWAYSNAVIGATE = 0x4
EBO_NOTRAVELLOG = 0x8
EBO_NOWRAPPERWINDOW = 0x10
EBF_NONE = 0
EBF_SELECTFROMDATAOBJECT = 0x100
EBF_NODROPTARGET = 0x200
ECS_ENABLED = 0
ECS_DISABLED = 0x1
ECS_HIDDEN = 0x2
ECS_CHECKBOX = 0x4
ECS_CHECKED = 0x8

ECF_HASSUBCOMMANDS = 0x1
ECF_HASSPLITBUTTON = 0x2
ECF_HIDELABEL = 0x4
ECF_ISSEPARATOR = 0x8
ECF_HASLUASHIELD = 0x10

SIATTRIBFLAGS_AND = 0x1
SIATTRIBFLAGS_OR = 0x2
SIATTRIBFLAGS_APPCOMPAT = 0x3
SIATTRIBFLAGS_MASK = 0x3

SIGDN_NORMALDISPLAY = 0
SIGDN_PARENTRELATIVEPARSING = -2147385343  ## 0x80018001
SIGDN_DESKTOPABSOLUTEPARSING = -2147319808  ## 0x80028000
SIGDN_PARENTRELATIVEEDITING = -2147282943  ## 0x80031001
SIGDN_DESKTOPABSOLUTEEDITING = -2147172352  ## 0x8004c000
SIGDN_FILESYSPATH = -2147123200  ## 0x80058000
SIGDN_URL = -2147057664  ## 0x80068000
SIGDN_PARENTRELATIVEFORADDRESSBAR = -2146975743  ## 0x8007c001,
SIGDN_PARENTRELATIVE = -2146959359  ## 0x80080001

SICHINT_DISPLAY = (0,)
SICHINT_ALLFIELDS = -2147483648  ## 0x80000000
SICHINT_CANONICAL = 0x10000000

ASSOCCLASS_SHELL_KEY = 0
ASSOCCLASS_PROGID_KEY = 1  # hkeyClass
ASSOCCLASS_PROGID_STR = 2  # pszClass (HKCR\pszClass)
ASSOCCLASS_CLSID_KEY = 3  # hkeyClass
ASSOCCLASS_CLSID_STR = 4  #  pszClass (HKCR\CLSID\pszClass)
ASSOCCLASS_APP_KEY = 5  # hkeyClass
ASSOCCLASS_APP_STR = 6  # pszClass (HKCR\Applications\PathFindFileName(pszClass))
ASSOCCLASS_SYSTEM_STR = 7  # pszClass
ASSOCCLASS_FOLDER = 8  # none
ASSOCCLASS_STAR = 9  # none

NSTCS_HASEXPANDOS = 0x1
NSTCS_HASLINES = 0x2
NSTCS_SINGLECLICKEXPAND = 0x4
NSTCS_FULLROWSELECT = 0x8
NSTCS_SPRINGEXPAND = 0x10
NSTCS_HORIZONTALSCROLL = 0x20
NSTCS_ROOTHASEXPANDO = 0x40
NSTCS_SHOWSELECTIONALWAYS = 0x80
NSTCS_NOINFOTIP = 0x200
NSTCS_EVENHEIGHT = 0x400
NSTCS_NOREPLACEOPEN = 0x800
NSTCS_DISABLEDRAGDROP = 0x1000
NSTCS_NOORDERSTREAM = 0x2000
NSTCS_RICHTOOLTIP = 0x4000
NSTCS_BORDER = 0x8000
NSTCS_NOEDITLABELS = 0x10000
NSTCS_TABSTOP = 0x20000
NSTCS_FAVORITESMODE = 0x80000
NSTCS_AUTOHSCROLL = 0x100000
NSTCS_FADEINOUTEXPANDOS = 0x200000
NSTCS_EMPTYTEXT = 0x400000
NSTCS_CHECKBOXES = 0x800000
NSTCS_PARTIALCHECKBOXES = 0x1000000
NSTCS_EXCLUSIONCHECKBOXES = 0x2000000
NSTCS_DIMMEDCHECKBOXES = 0x4000000
NSTCS_NOINDENTCHECKS = 0x8000000
NSTCS_ALLOWJUNCTIONS = 0x10000000
NSTCS_SHOWTABSBUTTON = 0x20000000
NSTCS_SHOWDELETEBUTTON = 0x40000000
NSTCS_SHOWREFRESHBUTTON = -2147483648  # 0x80000000

NSTCRS_VISIBLE = 0
NSTCRS_HIDDEN = 0x1
NSTCRS_EXPANDED = 0x2
NSTCIS_NONE = 0
NSTCIS_SELECTED = 0x1
NSTCIS_EXPANDED = 0x2
NSTCIS_BOLD = 0x4
NSTCIS_DISABLED = 0x8
NSTCGNI_NEXT = 0
NSTCGNI_NEXTVISIBLE = 0x1
NSTCGNI_PREV = 0x2
NSTCGNI_PREVVISIBLE = 0x3
NSTCGNI_PARENT = 0x4
NSTCGNI_CHILD = 0x5
NSTCGNI_FIRSTVISIBLE = 0x6
NSTCGNI_LASTVISIBLE = 0x7

CLSID_ExplorerBrowser = "{71f96385-ddd6-48d3-a0c1-ae06e8b055fb}"

# Names of the methods of many shell interfaces; used by implementation of
# the interfaces.
IBrowserFrame_Methods = ["GetFrameOptions"]
ICategorizer_Methods = [
    "GetDescription",
    "GetCategory",
    "GetCategoryInfo",
    "CompareCategory",
]
ICategoryProvider_Methods = [
    "CanCategorizeOnSCID",
    "GetDefaultCategory",
    "GetCategoryForSCID",
    "EnumCategories",
    "GetCategoryName",
    "CreateCategory",
]
IContextMenu_Methods = ["QueryContextMenu", "InvokeCommand", "GetCommandString"]
IExplorerCommand_Methods = [
    "GetTitle",
    "GetIcon",
    "GetToolTip",
    "GetCanonicalName",
    "GetState",
    "Invoke",
    "GetFlags",
    "EnumSubCommands",
]
IExplorerCommandProvider_Methods = ["GetCommand", "GetCommands"]
IOleWindow_Methods = [
    "GetWindow",
    "ContextSensitiveHelp",
]  # XXX - this should be somewhere in win32com
IPersist_Methods = ["GetClassID"]
IPersistFolder_Methods = IPersist_Methods + ["Initialize"]
IPersistFolder2_Methods = IPersistFolder_Methods + ["GetCurFolder"]
IShellExtInit_Methods = ["Initialize"]
IShellView_Methods = IOleWindow_Methods + [
    "TranslateAccelerator",
    "EnableModeless",
    "UIActivate",
    "Refresh",
    "CreateViewWindow",
    "DestroyViewWindow",
    "GetCurrentInfo",
    "AddPropertySheetPages",
    "SaveViewState",
    "SelectItem",
    "GetItemObject",
]

IShellFolder_Methods = [
    "ParseDisplayName",
    "EnumObjects",
    "BindToObject",
    "BindToStorage",
    "CompareIDs",
    "CreateViewObject",
    "GetAttributesOf",
    "GetUIObjectOf",
    "GetDisplayNameOf",
    "SetNameOf",
]
IShellFolder2_Methods = IShellFolder_Methods + [
    "GetDefaultSearchGUID",
    "EnumSearches",
    "GetDefaultColumn",
    "GetDefaultColumnState",
    "GetDetailsEx",
    "GetDetailsOf",
    "MapColumnToSCID",
]

## enum GETPROPERTYSTOREFLAGS, used with IShellItem2 methods
GPS_DEFAULT = 0
GPS_HANDLERPROPERTIESONLY = 0x1
GPS_READWRITE = 0x2
GPS_TEMPORARY = 0x4
GPS_FASTPROPERTIESONLY = 0x8
GPS_OPENSLOWITEM = 0x10
GPS_DELAYCREATION = 0x20
GPS_BESTEFFORT = 0x40
GPS_MASK_VALID = 0x7F

## Bind context parameter names, used with IBindCtx::RegisterObjectParam
STR_AVOID_DRIVE_RESTRICTION_POLICY = "Avoid Drive Restriction Policy"
STR_BIND_DELEGATE_CREATE_OBJECT = "Delegate Object Creation"
STR_BIND_FOLDERS_READ_ONLY = "Folders As Read Only"
STR_BIND_FOLDER_ENUM_MODE = "Folder Enum Mode"
STR_BIND_FORCE_FOLDER_SHORTCUT_RESOLVE = "Force Folder Shortcut Resolve"
STR_DONT_PARSE_RELATIVE = "Don't Parse Relative"
STR_DONT_RESOLVE_LINK = "Don't Resolve Link"
## STR_ENUM_ITEMS_FLAGS
STR_FILE_SYS_BIND_DATA = "File System Bind Data"
STR_GET_ASYNC_HANDLER = "GetAsyncHandler"
STR_GPS_BESTEFFORT = "GPS_BESTEFFORT"
STR_GPS_DELAYCREATION = "GPS_DELAYCREATION"
STR_GPS_FASTPROPERTIESONLY = "GPS_FASTPROPERTIESONLY"
STR_GPS_HANDLERPROPERTIESONLY = "GPS_HANDLERPROPERTIESONLY"
STR_GPS_NO_OPLOCK = "GPS_NO_OPLOCK"
STR_GPS_OPENSLOWITEM = "GPS_OPENSLOWITEM"
STR_IFILTER_FORCE_TEXT_FILTER_FALLBACK = "Always bind persistent handlers"
STR_IFILTER_LOAD_DEFINED_FILTER = "Only bind registered persistent handlers"
STR_INTERNAL_NAVIGATE = "Internal Navigation"
STR_INTERNETFOLDER_PARSE_ONLY_URLMON_BINDABLE = "Validate URL"
STR_ITEM_CACHE_CONTEXT = "ItemCacheContext"
STR_NO_VALIDATE_FILENAME_CHARS = "NoValidateFilenameChars"
STR_PARSE_ALLOW_INTERNET_SHELL_FOLDERS = "Allow binding to Internet shell folder handlers and negate STR_PARSE_PREFER_WEB_BROWSING"
STR_PARSE_AND_CREATE_ITEM = "ParseAndCreateItem"
STR_PARSE_DONT_REQUIRE_VALIDATED_URLS = "Do not require validated URLs"
STR_PARSE_EXPLICIT_ASSOCIATION_SUCCESSFUL = "ExplicitAssociationSuccessful"
STR_PARSE_PARTIAL_IDLIST = "ParseOriginalItem"
STR_PARSE_PREFER_FOLDER_BROWSING = "Parse Prefer Folder Browsing"
STR_PARSE_PREFER_WEB_BROWSING = "Do not bind to Internet shell folder handlers"
STR_PARSE_PROPERTYSTORE = "DelegateNamedProperties"
STR_PARSE_SHELL_PROTOCOL_TO_FILE_OBJECTS = "Parse Shell Protocol To File Objects"
STR_PARSE_SHOW_NET_DIAGNOSTICS_UI = "Show network diagnostics UI"
STR_PARSE_SKIP_NET_CACHE = "Skip Net Resource Cache"
STR_PARSE_TRANSLATE_ALIASES = "Parse Translate Aliases"
STR_PARSE_WITH_EXPLICIT_ASSOCAPP = "ExplicitAssociationApp"
STR_PARSE_WITH_EXPLICIT_PROGID = "ExplicitProgid"
STR_PARSE_WITH_PROPERTIES = "ParseWithProperties"
## STR_PROPERTYBAG_PARAM
STR_SKIP_BINDING_CLSID = "Skip Binding CLSID"
STR_TRACK_CLSID = "Track the CLSID"

## KF_REDIRECTION_CAPABILITIES enum
KF_REDIRECTION_CAPABILITIES_ALLOW_ALL = 0x000000FF
KF_REDIRECTION_CAPABILITIES_REDIRECTABLE = 0x00000001
KF_REDIRECTION_CAPABILITIES_DENY_ALL = 0x000FFF00
KF_REDIRECTION_CAPABILITIES_DENY_POLICY_REDIRECTED = 0x00000100
KF_REDIRECTION_CAPABILITIES_DENY_POLICY = 0x00000200
KF_REDIRECTION_CAPABILITIES_DENY_PERMISSIONS = 0x00000400

## KF_REDIRECT_FLAGS enum
KF_REDIRECT_USER_EXCLUSIVE = 0x00000001
KF_REDIRECT_COPY_SOURCE_DACL = 0x00000002
KF_REDIRECT_OWNER_USER = 0x00000004
KF_REDIRECT_SET_OWNER_EXPLICIT = 0x00000008
KF_REDIRECT_CHECK_ONLY = 0x00000010
KF_REDIRECT_WITH_UI = 0x00000020
KF_REDIRECT_UNPIN = 0x00000040
KF_REDIRECT_PIN = 0x00000080
KF_REDIRECT_COPY_CONTENTS = 0x00000200
KF_REDIRECT_DEL_SOURCE_CONTENTS = 0x00000400
KF_REDIRECT_EXCLUDE_ALL_KNOWN_SUBFOLDERS = 0x00000800

## KF_CATEGORY enum
KF_CATEGORY_VIRTUAL = 0x00000001
KF_CATEGORY_FIXED = 0x00000002
KF_CATEGORY_COMMON = 0x00000003
KF_CATEGORY_PERUSER = 0x00000004

## FFFP_MODE enum
FFFP_EXACTMATCH = 0
FFFP_NEARESTPARENTMATCH = 1

## APPDOCLISTTYPE, used with IApplicationDocumentLists.GetList
ADLT_RECENT = 0
ADLT_FREQUENT = 1

## KNOWNDESTCATEGORY used with ICustomDestinationList.AppendKnownCategory
KDC_FREQUENT = 1
KDC_RECENT = 2

## LIBRARYFOLDERFILTER used with IShellLibrary.GetFolders
LFF_FORCEFILESYSTEM = 1
LFF_STORAGEITEMS = 2
LFF_ALLITEMS = 3

## DEFAULTSAVEFOLDERTYPE used with IShellLibrary.Get/SetDefaultSaveFolder
DSFT_DETECT = 1
DSFT_PRIVATE = 2
DSFT_PUBLIC = 3

## LIBRARYOPTIONFLAGS used with IShellLibrary.Get/SetOptions
LOF_DEFAULT = 0
LOF_PINNEDTONAVPANE = 1
LOF_MASK_ALL = 1

## LIBRARYSAVEFLAGS Used with PyIShellLibrary.Save
LSF_FAILIFTHERE = 0
LSF_OVERRIDEEXISTING = 1
LSF_MAKEUNIQUENAME = 2

## TRANSFER_SOURCE_FLAGS, used with IFileOperationProgressSink
TSF_NORMAL = 0
TSF_FAIL_EXIST = 0
TSF_RENAME_EXIST = 0x1
TSF_OVERWRITE_EXIST = 0x2
TSF_ALLOW_DECRYPTION = 0x4
TSF_NO_SECURITY = 0x8
TSF_COPY_CREATION_TIME = 0x10
TSF_COPY_WRITE_TIME = 0x20
TSF_USE_FULL_ACCESS = 0x40
TSF_DELETE_RECYCLE_IF_POSSIBLE = 0x80
TSF_COPY_HARD_LINK = 0x100
TSF_COPY_LOCALIZED_NAME = 0x200
TSF_MOVE_AS_COPY_DELETE = 0x400
TSF_SUSPEND_SHELLEVENTS = 0x800

## TRANSFER_ADVISE_STATE, used with ITransferAdviseSink
TS_NONE = 0
TS_PERFORMING = 1
TS_PREPARING = 2
TS_INDETERMINATE = 4

## Success HRESULTs returned by ITransfer* interface operations
COPYENGINE_S_YES = 0x00270001
COPYENGINE_S_NOT_HANDLED = 0x00270003
COPYENGINE_S_USER_RETRY = 0x00270004
COPYENGINE_S_USER_IGNORED = 0x00270005
COPYENGINE_S_MERGE = 0x00270006
COPYENGINE_S_DONT_PROCESS_CHILDREN = 0x00270008
COPYENGINE_S_ALREADY_DONE = 0x0027000A
COPYENGINE_S_PENDING = 0x0027000B
COPYENGINE_S_KEEP_BOTH = 0x0027000C
COPYENGINE_S_CLOSE_PROGRAM = 0x0027000D
COPYENGINE_S_COLLISIONRESOLVED = 0x0027000E

## Error HRESULTS
COPYENGINE_E_USER_CANCELLED = 0x80270000
COPYENGINE_E_CANCELLED = 0x80270001
COPYENGINE_E_REQUIRES_ELEVATION = 0x80270002
COPYENGINE_E_SAME_FILE = 0x80270003
COPYENGINE_E_DIFF_DIR = 0x80270004
COPYENGINE_E_MANY_SRC_1_DEST = 0x80270005
COPYENGINE_E_DEST_SUBTREE = 0x80270009
COPYENGINE_E_DEST_SAME_TREE = 0x8027000A
COPYENGINE_E_FLD_IS_FILE_DEST = 0x8027000B
COPYENGINE_E_FILE_IS_FLD_DEST = 0x8027000C
COPYENGINE_E_FILE_TOO_LARGE = 0x8027000D
COPYENGINE_E_REMOVABLE_FULL = 0x8027000E
COPYENGINE_E_DEST_IS_RO_CD = 0x8027000F
COPYENGINE_E_DEST_IS_RW_CD = 0x80270010
COPYENGINE_E_DEST_IS_R_CD = 0x80270011
COPYENGINE_E_DEST_IS_RO_DVD = 0x80270012
COPYENGINE_E_DEST_IS_RW_DVD = 0x80270013
COPYENGINE_E_DEST_IS_R_DVD = 0x80270014
COPYENGINE_E_SRC_IS_RO_CD = 0x80270015
COPYENGINE_E_SRC_IS_RW_CD = 0x80270016
COPYENGINE_E_SRC_IS_R_CD = 0x80270017
COPYENGINE_E_SRC_IS_RO_DVD = 0x80270018
COPYENGINE_E_SRC_IS_RW_DVD = 0x80270019
COPYENGINE_E_SRC_IS_R_DVD = 0x8027001A
COPYENGINE_E_INVALID_FILES_SRC = 0x8027001B
COPYENGINE_E_INVALID_FILES_DEST = 0x8027001C
COPYENGINE_E_PATH_TOO_DEEP_SRC = 0x8027001D
COPYENGINE_E_PATH_TOO_DEEP_DEST = 0x8027001E
COPYENGINE_E_ROOT_DIR_SRC = 0x8027001F
COPYENGINE_E_ROOT_DIR_DEST = 0x80270020
COPYENGINE_E_ACCESS_DENIED_SRC = 0x80270021
COPYENGINE_E_ACCESS_DENIED_DEST = 0x80270022
COPYENGINE_E_PATH_NOT_FOUND_SRC = 0x80270023
COPYENGINE_E_PATH_NOT_FOUND_DEST = 0x80270024
COPYENGINE_E_NET_DISCONNECT_SRC = 0x80270025
COPYENGINE_E_NET_DISCONNECT_DEST = 0x80270026
COPYENGINE_E_SHARING_VIOLATION_SRC = 0x80270027
COPYENGINE_E_SHARING_VIOLATION_DEST = 0x80270028
COPYENGINE_E_ALREADY_EXISTS_NORMAL = 0x80270029
COPYENGINE_E_ALREADY_EXISTS_READONLY = 0x8027002A
COPYENGINE_E_ALREADY_EXISTS_SYSTEM = 0x8027002B
COPYENGINE_E_ALREADY_EXISTS_FOLDER = 0x8027002C
COPYENGINE_E_STREAM_LOSS = 0x8027002D
COPYENGINE_E_EA_LOSS = 0x8027002E
COPYENGINE_E_PROPERTY_LOSS = 0x8027002F
COPYENGINE_E_PROPERTIES_LOSS = 0x80270030
COPYENGINE_E_ENCRYPTION_LOSS = 0x80270031
COPYENGINE_E_DISK_FULL = 0x80270032
COPYENGINE_E_DISK_FULL_CLEAN = 0x80270033
COPYENGINE_E_EA_NOT_SUPPORTED = 0x80270034
COPYENGINE_E_CANT_REACH_SOURCE = 0x80270035
COPYENGINE_E_RECYCLE_UNKNOWN_ERROR = 0x80270035
COPYENGINE_E_RECYCLE_FORCE_NUKE = 0x80270036
COPYENGINE_E_RECYCLE_SIZE_TOO_BIG = 0x80270037
COPYENGINE_E_RECYCLE_PATH_TOO_LONG = 0x80270038
COPYENGINE_E_RECYCLE_BIN_NOT_FOUND = 0x8027003A
COPYENGINE_E_NEWFILE_NAME_TOO_LONG = 0x8027003B
COPYENGINE_E_NEWFOLDER_NAME_TOO_LONG = 0x8027003C
COPYENGINE_E_DIR_NOT_EMPTY = 0x8027003D
COPYENGINE_E_FAT_MAX_IN_ROOT = 0x8027003E
COPYENGINE_E_ACCESSDENIED_READONLY = 0x8027003F
COPYENGINE_E_REDIRECTED_TO_WEBPAGE = 0x80270040
COPYENGINE_E_SERVER_BAD_FILE_TYPE = 0x80270041

FOLDERID_NetworkFolder = "{D20BEEC4-5CA8-4905-AE3B-BF251EA09B53}"
FOLDERID_ComputerFolder = "{0AC0837C-BBF8-452A-850D-79D08E667CA7}"
FOLDERID_InternetFolder = "{4D9F7874-4E0C-4904-967B-40B0D20C3E4B}"
FOLDERID_ControlPanelFolder = "{82A74AEB-AEB4-465C-A014-D097EE346D63}"
FOLDERID_PrintersFolder = "{76FC4E2D-D6AD-4519-A663-37BD56068185}"
FOLDERID_SyncManagerFolder = "{43668BF8-C14E-49B2-97C9-747784D784B7}"
FOLDERID_SyncSetupFolder = "{0F214138-B1D3-4a90-BBA9-27CBC0C5389A}"
FOLDERID_ConflictFolder = "{4bfefb45-347d-4006-a5be-ac0cb0567192}"
FOLDERID_SyncResultsFolder = "{289a9a43-be44-4057-a41b-587a76d7e7f9}"
FOLDERID_RecycleBinFolder = "{B7534046-3ECB-4C18-BE4E-64CD4CB7D6AC}"
FOLDERID_ConnectionsFolder = "{6F0CD92B-2E97-45D1-88FF-B0D186B8DEDD}"
FOLDERID_Fonts = "{FD228CB7-AE11-4AE3-864C-16F3910AB8FE}"
FOLDERID_Desktop = "{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}"
FOLDERID_Startup = "{B97D20BB-F46A-4C97-BA10-5E3608430854}"
FOLDERID_Programs = "{A77F5D77-2E2B-44C3-A6A2-ABA601054A51}"
FOLDERID_StartMenu = "{625B53C3-AB48-4EC1-BA1F-A1EF4146FC19}"
FOLDERID_Recent = "{AE50C081-EBD2-438A-8655-8A092E34987A}"
FOLDERID_SendTo = "{8983036C-27C0-404B-8F08-102D10DCFD74}"
FOLDERID_Documents = "{FDD39AD0-238F-46AF-ADB4-6C85480369C7}"
FOLDERID_Favorites = "{1777F761-68AD-4D8A-87BD-30B759FA33DD}"
FOLDERID_NetHood = "{C5ABBF53-E17F-4121-8900-86626FC2C973}"
FOLDERID_PrintHood = "{9274BD8D-CFD1-41C3-B35E-B13F55A758F4}"
FOLDERID_Templates = "{A63293E8-664E-48DB-A079-DF759E0509F7}"
FOLDERID_CommonStartup = "{82A5EA35-D9CD-47C5-9629-E15D2F714E6E}"
FOLDERID_CommonPrograms = "{0139D44E-6AFE-49F2-8690-3DAFCAE6FFB8}"
FOLDERID_CommonStartMenu = "{A4115719-D62E-491D-AA7C-E74B8BE3B067}"
FOLDERID_PublicDesktop = "{C4AA340D-F20F-4863-AFEF-F87EF2E6BA25}"
FOLDERID_ProgramData = "{62AB5D82-FDC1-4DC3-A9DD-070D1D495D97}"
FOLDERID_CommonTemplates = "{B94237E7-57AC-4347-9151-B08C6C32D1F7}"
FOLDERID_PublicDocuments = "{ED4824AF-DCE4-45A8-81E2-FC7965083634}"
FOLDERID_RoamingAppData = "{3EB685DB-65F9-4CF6-A03A-E3EF65729F3D}"
FOLDERID_LocalAppData = "{F1B32785-6FBA-4FCF-9D55-7B8E7F157091}"
FOLDERID_LocalAppDataLow = "{A520A1A4-1780-4FF6-BD18-167343C5AF16}"
FOLDERID_InternetCache = "{352481E8-33BE-4251-BA85-6007CAEDCF9D}"
FOLDERID_Cookies = "{2B0F765D-C0E9-4171-908E-08A611B84FF6}"
FOLDERID_History = "{D9DC8A3B-B784-432E-A781-5A1130A75963}"
FOLDERID_System = "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}"
FOLDERID_SystemX86 = "{D65231B0-B2F1-4857-A4CE-A8E7C6EA7D27}"
FOLDERID_Windows = "{F38BF404-1D43-42F2-9305-67DE0B28FC23}"
FOLDERID_Profile = "{5E6C858F-0E22-4760-9AFE-EA3317B67173}"
FOLDERID_Pictures = "{33E28130-4E1E-4676-835A-98395C3BC3BB}"
FOLDERID_ProgramFilesX86 = "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}"
FOLDERID_ProgramFilesCommonX86 = "{DE974D24-D9C6-4D3E-BF91-F4455120B917}"
FOLDERID_ProgramFilesX64 = "{6D809377-6AF0-444b-8957-A3773F02200E}"
FOLDERID_ProgramFilesCommonX64 = "{6365D5A7-0F0D-45e5-87F6-0DA56B6A4F7D}"
FOLDERID_ProgramFiles = "{905e63b6-c1bf-494e-b29c-65b732d3d21a}"
FOLDERID_ProgramFilesCommon = "{F7F1ED05-9F6D-47A2-AAAE-29D317C6F066}"
FOLDERID_UserProgramFiles = "{5cd7aee2-2219-4a67-b85d-6c9ce15660cb}"
FOLDERID_UserProgramFilesCommon = "{bcbd3057-ca5c-4622-b42d-bc56db0ae516}"
FOLDERID_AdminTools = "{724EF170-A42D-4FEF-9F26-B60E846FBA4F}"
FOLDERID_CommonAdminTools = "{D0384E7D-BAC3-4797-8F14-CBA229B392B5}"
FOLDERID_Music = "{4BD8D571-6D19-48D3-BE97-422220080E43}"
FOLDERID_Videos = "{18989B1D-99B5-455B-841C-AB7C74E4DDFC}"
FOLDERID_Ringtones = "{C870044B-F49E-4126-A9C3-B52A1FF411E8}"
FOLDERID_PublicPictures = "{B6EBFB86-6907-413C-9AF7-4FC2ABF07CC5}"
FOLDERID_PublicMusic = "{3214FAB5-9757-4298-BB61-92A9DEAA44FF}"
FOLDERID_PublicVideos = "{2400183A-6185-49FB-A2D8-4A392A602BA3}"
FOLDERID_PublicRingtones = "{E555AB60-153B-4D17-9F04-A5FE99FC15EC}"
FOLDERID_ResourceDir = "{8AD10C31-2ADB-4296-A8F7-E4701232C972}"
FOLDERID_LocalizedResourcesDir = "{2A00375E-224C-49DE-B8D1-440DF7EF3DDC}"
FOLDERID_CommonOEMLinks = "{C1BAE2D0-10DF-4334-BEDD-7AA20B227A9D}"
FOLDERID_CDBurning = "{9E52AB10-F80D-49DF-ACB8-4330F5687855}"
FOLDERID_UserProfiles = "{0762D272-C50A-4BB0-A382-697DCD729B80}"
FOLDERID_Playlists = "{DE92C1C7-837F-4F69-A3BB-86E631204A23}"
FOLDERID_SamplePlaylists = "{15CA69B3-30EE-49C1-ACE1-6B5EC372AFB5}"
FOLDERID_SampleMusic = "{B250C668-F57D-4EE1-A63C-290EE7D1AA1F}"
FOLDERID_SamplePictures = "{C4900540-2379-4C75-844B-64E6FAF8716B}"
FOLDERID_SampleVideos = "{859EAD94-2E85-48AD-A71A-0969CB56A6CD}"
FOLDERID_PhotoAlbums = "{69D2CF90-FC33-4FB7-9A0C-EBB0F0FCB43C}"
FOLDERID_Public = "{DFDF76A2-C82A-4D63-906A-5644AC457385}"
FOLDERID_ChangeRemovePrograms = "{df7266ac-9274-4867-8d55-3bd661de872d}"
FOLDERID_AppUpdates = "{a305ce99-f527-492b-8b1a-7e76fa98d6e4}"
FOLDERID_AddNewPrograms = "{de61d971-5ebc-4f02-a3a9-6c82895e5c04}"
FOLDERID_Downloads = "{374DE290-123F-4565-9164-39C4925E467B}"
FOLDERID_PublicDownloads = "{3D644C9B-1FB8-4f30-9B45-F670235F79C0}"
FOLDERID_SavedSearches = "{7d1d3a04-debb-4115-95cf-2f29da2920da}"
FOLDERID_QuickLaunch = "{52a4f021-7b75-48a9-9f6b-4b87a210bc8f}"
FOLDERID_Contacts = "{56784854-C6CB-462b-8169-88E350ACB882}"
FOLDERID_SidebarParts = "{A75D362E-50FC-4fb7-AC2C-A8BEAA314493}"
FOLDERID_SidebarDefaultParts = "{7B396E54-9EC5-4300-BE0A-2482EBAE1A26}"
FOLDERID_PublicGameTasks = "{DEBF2536-E1A8-4c59-B6A2-414586476AEA}"
FOLDERID_GameTasks = "{054FAE61-4DD8-4787-80B6-090220C4B700}"
FOLDERID_SavedGames = "{4C5C32FF-BB9D-43b0-B5B4-2D72E54EAAA4}"
FOLDERID_Games = "{CAC52C1A-B53D-4edc-92D7-6B2E8AC19434}"
FOLDERID_SEARCH_MAPI = "{98ec0e18-2098-4d44-8644-66979315a281}"
FOLDERID_SEARCH_CSC = "{ee32e446-31ca-4aba-814f-a5ebd2fd6d5e}"
FOLDERID_Links = "{bfb9d5e0-c6a9-404c-b2b2-ae6db6af4968}"
FOLDERID_UsersFiles = "{f3ce0f7c-4901-4acc-8648-d5d44b04ef8f}"
FOLDERID_UsersLibraries = "{A302545D-DEFF-464b-ABE8-61C8648D939B}"
FOLDERID_SearchHome = "{190337d1-b8ca-4121-a639-6d472d16972a}"
FOLDERID_OriginalImages = "{2C36C0AA-5812-4b87-BFD0-4CD0DFB19B39}"
FOLDERID_DocumentsLibrary = "{7b0db17d-9cd2-4a93-9733-46cc89022e7c}"
FOLDERID_MusicLibrary = "{2112AB0A-C86A-4ffe-A368-0DE96E47012E}"
FOLDERID_PicturesLibrary = "{A990AE9F-A03B-4e80-94BC-9912D7504104}"
FOLDERID_VideosLibrary = "{491E922F-5643-4af4-A7EB-4E7A138D8174}"
FOLDERID_RecordedTVLibrary = "{1A6FDBA2-F42D-4358-A798-B74D745926C5}"
FOLDERID_HomeGroup = "{52528A6B-B9E3-4add-B60D-588C2DBA842D}"
FOLDERID_HomeGroupCurrentUser = "{9B74B6A3-0DFD-4f11-9E78-5F7800F2E772}"
FOLDERID_DeviceMetadataStore = "{5CE4A5E9-E4EB-479D-B89F-130C02886155}"
FOLDERID_Libraries = "{1B3EA5DC-B587-4786-B4EF-BD1DC332AEAE}"
FOLDERID_PublicLibraries = "{48daf80b-e6cf-4f4e-b800-0e69d84ee384}"
FOLDERID_UserPinned = "{9e3995ab-1f9c-4f13-b827-48b24b6c7174}"
FOLDERID_ImplicitAppShortcuts = "{************************************}"
FOLDERID_AccountPictures = "{008ca0b1-55b4-4c56-b8a8-4de4b299d3be}"
FOLDERID_PublicUserTiles = "{0482af6c-08f1-4c34-8c90-e17ec98b1e17}"
FOLDERID_AppsFolder = "{1e87508d-89c2-42f0-8a7e-645a0f50ca58}"
FOLDERID_StartMenuAllPrograms = "{F26305EF-6948-40B9-B255-81453D09C785}"
FOLDERID_CommonStartMenuPlaces = "{A440879F-87A0-4F7D-B700-0207B966194A}"
FOLDERID_ApplicationShortcuts = "{A3918781-E5F2-4890-B3D9-A7E54332328C}"
FOLDERID_RoamingTiles = "{00BCFC5A-ED94-4e48-96A1-3F6217F21990}"
FOLDERID_RoamedTileImages = "{AAA8D5A5-F1D6-4259-BAA8-78E7EF60835E}"
FOLDERID_Screenshots = "{b7bede81-df94-4682-a7d8-57a52620b86f}"
FOLDERID_CameraRoll = "{AB5FB87B-7CE2-4F83-915D-550846C9537B}"
FOLDERID_SkyDrive = "{A52BBA46-E9E1-435f-B3D9-28DAA648C0F6}"
FOLDERID_OneDrive = "{A52BBA46-E9E1-435f-B3D9-28DAA648C0F6}"
FOLDERID_SkyDriveDocuments = "{24D89E24-2F19-4534-9DDE-6A6671FBB8FE}"
FOLDERID_SkyDrivePictures = "{339719B5-8C47-4894-94C2-D8F77ADD44A6}"
FOLDERID_SkyDriveMusic = "{C3F2459E-80D6-45DC-BFEF-1F769F2BE730}"
FOLDERID_SkyDriveCameraRoll = "{767E6811-49CB-4273-87C2-20F355E1085B}"
FOLDERID_SearchHistory = "{0D4C3DB6-03A3-462F-A0E6-08924C41B5D4}"
FOLDERID_SearchTemplates = "{7E636BFE-DFA9-4D5E-B456-D7B39851D8A9}"
FOLDERID_CameraRollLibrary = "{2B20DF75-1EDA-4039-8097-38798227D5B7}"
FOLDERID_SavedPictures = "{3B193882-D3AD-4eab-965A-69829D1FB59F}"
FOLDERID_SavedPicturesLibrary = "{E25B5812-BE88-4bd9-94B0-29233477B6C3}"
FOLDERID_RetailDemo = "{12D4C69E-24AD-4923-BE19-31321C43A767}"
FOLDERID_Device = "{1C2AC1DC-4358-4B6C-9733-AF21156576F0}"
FOLDERID_DevelopmentFiles = "{DBE8E08E-3053-4BBC-B183-2A7B2B191E59}"
FOLDERID_Objects3D = "{31C0DD25-9439-4F12-BF41-7FF4EDA38722}"
FOLDERID_AppCaptures = "{EDC0FE71-98D8-4F4A-B920-C8DC133CB165}"
FOLDERID_LocalDocuments = "{f42ee2d3-909f-4907-8871-4c22fc0bf756}"
FOLDERID_LocalPictures = "{0ddd015d-b06c-45d5-8c4c-f59713854639 }"
FOLDERID_LocalVideos = "{35286a68-3c57-41a1-bbb1-0eae73d76c95}"
FOLDERID_LocalMusic = "{a0c69a99-21c8-4671-8703-7934162fcf1d}"
FOLDERID_LocalDownloads = "{7d83ee9b-2244-4e70-b1f5-5393042af1e4}"
FOLDERID_RecordedCalls = "{2f8b40c2-83ed-48ee-b383-a1f157ec6f9a}"

KF_FLAG_DEFAULT = 0x00000000
KF_FLAG_FORCE_APP_DATA_REDIRECTION = 0x00080000
KF_FLAG_RETURN_FILTER_REDIRECTION_TARGET = 0x00040000
KF_FLAG_FORCE_PACKAGE_REDIRECTION = 0x00020000
KF_FLAG_NO_PACKAGE_REDIRECTION = 0x00010000
KF_FLAG_FORCE_APPCONTAINER_REDIRECTION = 0x00020000
KF_FLAG_NO_APPCONTAINER_REDIRECTION = 0x00010000
KF_FLAG_CREATE = 0x00008000
KF_FLAG_DONT_VERIFY = 0x00004000
KF_FLAG_DONT_UNEXPAND = 0x00002000
KF_FLAG_NO_ALIAS = 0x00001000
KF_FLAG_INIT = 0x00000800
KF_FLAG_DEFAULT_PATH = 0x00000400
KF_FLAG_NOT_PARENT_RELATIVE = 0x00000200
KF_FLAG_SIMPLE_IDLIST = 0x00000100
KF_FLAG_ALIAS_ONLY = 0x80000000
