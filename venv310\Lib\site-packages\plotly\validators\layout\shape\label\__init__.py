import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._yanchor import <PERSON><PERSON>Valida<PERSON>
    from ._xanchor import X<PERSON><PERSON><PERSON><PERSON>da<PERSON>
    from ._texttemplate import TexttemplateValidator
    from ._textposition import Text<PERSON>Validator
    from ._textangle import <PERSON>ang<PERSON><PERSON><PERSON>da<PERSON>
    from ._text import <PERSON>Valida<PERSON>
    from ._padding import Padding<PERSON>alida<PERSON>
    from ._font import <PERSON><PERSON><PERSON>alida<PERSON>
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yanchor.YanchorValidator",
            "._xanchor.XanchorValidator",
            "._texttemplate.TexttemplateValidator",
            "._textposition.TextpositionValidator",
            "._textangle.TextangleValidator",
            "._text.TextValidator",
            "._padding.PaddingValidator",
            "._font.FontValidator",
        ],
    )
