#                   --- THIS FILE IS AUTO-GENERATED ---
# Modifications will be overwitten the next time code generation run.

import _plotly_utils.basevalidators as _bv


class DrawdirectionValidator(_bv.EnumeratedValidator):
    def __init__(
        self, plotly_name="drawdirection", parent_name="layout.newshape", **kwargs
    ):
        super().__init__(
            plotly_name,
            parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            values=kwargs.pop(
                "values", ["ortho", "horizontal", "vertical", "diagonal"]
            ),
            **kwargs,
        )
