import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._zsrc import ZsrcVali<PERSON>tor
    from ._zsmooth import ZsmoothValidator
    from ._zorder import ZorderValidator
    from ._zmin import <PERSON>minVali<PERSON><PERSON>
    from ._zmax import <PERSON>maxV<PERSON><PERSON><PERSON>
    from ._z import <PERSON>V<PERSON><PERSON><PERSON>
    from ._yaxis import Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._y0 import Y0<PERSON><PERSON><PERSON><PERSON>
    from ._xaxis import XaxisValidator
    from ._x0 import X0Vali<PERSON><PERSON>
    from ._visible import VisibleValidator
    from ._uirevision import UirevisionValidator
    from ._uid import UidValidator
    from ._textsrc import TextsrcValidator
    from ._text import TextValidator
    from ._stream import StreamValidator
    from ._source import SourceValidator
    from ._opacity import OpacityValidator
    from ._name import NameValidator
    from ._metasrc import MetasrcValidator
    from ._meta import MetaValidator
    from ._legendwidth import LegendwidthValidator
    from ._legendrank import LegendrankValida<PERSON>
    from ._legendgrouptitle import LegendgrouptitleValida<PERSON>
    from ._legend import LegendVali<PERSON>tor
    from ._idssrc import IdssrcValida<PERSON>
    from ._ids import IdsValidator
    from ._hovertextsrc import HovertextsrcValidator
    from ._hovertext import HovertextValidator
    from ._hovertemplatesrc import HovertemplatesrcValidator
    from ._hovertemplate import HovertemplateValidator
    from ._hoverlabel import HoverlabelValidator
    from ._hoverinfosrc import HoverinfosrcValidator
    from ._hoverinfo import HoverinfoValidator
    from ._dy import DyValidator
    from ._dx import DxValidator
    from ._customdatasrc import CustomdatasrcValidator
    from ._customdata import CustomdataValidator
    from ._colormodel import ColormodelValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._zsrc.ZsrcValidator",
            "._zsmooth.ZsmoothValidator",
            "._zorder.ZorderValidator",
            "._zmin.ZminValidator",
            "._zmax.ZmaxValidator",
            "._z.ZValidator",
            "._yaxis.YaxisValidator",
            "._y0.Y0Validator",
            "._xaxis.XaxisValidator",
            "._x0.X0Validator",
            "._visible.VisibleValidator",
            "._uirevision.UirevisionValidator",
            "._uid.UidValidator",
            "._textsrc.TextsrcValidator",
            "._text.TextValidator",
            "._stream.StreamValidator",
            "._source.SourceValidator",
            "._opacity.OpacityValidator",
            "._name.NameValidator",
            "._metasrc.MetasrcValidator",
            "._meta.MetaValidator",
            "._legendwidth.LegendwidthValidator",
            "._legendrank.LegendrankValidator",
            "._legendgrouptitle.LegendgrouptitleValidator",
            "._legend.LegendValidator",
            "._idssrc.IdssrcValidator",
            "._ids.IdsValidator",
            "._hovertextsrc.HovertextsrcValidator",
            "._hovertext.HovertextValidator",
            "._hovertemplatesrc.HovertemplatesrcValidator",
            "._hovertemplate.HovertemplateValidator",
            "._hoverlabel.HoverlabelValidator",
            "._hoverinfosrc.HoverinfosrcValidator",
            "._hoverinfo.HoverinfoValidator",
            "._dy.DyValidator",
            "._dx.DxValidator",
            "._customdatasrc.CustomdatasrcValidator",
            "._customdata.CustomdataValidator",
            "._colormodel.ColormodelValidator",
        ],
    )
