../../Scripts/watchmedo.exe,sha256=VaEB53gFRANTbVEAu_uNA8Bi52VhYQHv90L57qwMmP0,108388
watchdog-6.0.0.dist-info/AUTHORS,sha256=JT4CdfYRf1V0Mg9RbNaaGK6LyP9pztEsJR6bQ-MfaG8,2928
watchdog-6.0.0.dist-info/COPYING,sha256=OfCBgo22-UxwEj-k-zDBvOPiFaj97OU6SZkf4HamnAg,703
watchdog-6.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
watchdog-6.0.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
watchdog-6.0.0.dist-info/METADATA,sha256=7P3gIwR-d2Hc4G1EiKvO3onjmDmZYaBS8vzLObAB4nk,44292
watchdog-6.0.0.dist-info/RECORD,,
watchdog-6.0.0.dist-info/WHEEL,sha256=mRFVR6BIY-4AcY-di6Z_WejDvSiiVIG1lUb-30eywqM,97
watchdog-6.0.0.dist-info/entry_points.txt,sha256=qt_Oe2U5Zlfz7LNA3PHipn3_1zlfRTp9dk3wTS3Ivb8,66
watchdog-6.0.0.dist-info/top_level.txt,sha256=OVdR7GkPGZako8sRtVuM0Nis-ZIElx3he3hKFPYnTGg,9
watchdog/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
watchdog/__pycache__/__init__.cpython-310.pyc,,
watchdog/__pycache__/events.cpython-310.pyc,,
watchdog/__pycache__/version.cpython-310.pyc,,
watchdog/__pycache__/watchmedo.cpython-310.pyc,,
watchdog/events.py,sha256=ig4oCNcm-mSe6rK5LPsIUcMO3dfq9EKegfM9I4SwVv8,16323
watchdog/observers/__init__.py,sha256=BmXKL1WXWnJP8-WRrhP4kmGK04gNADh1ifJezeZs4hs,3256
watchdog/observers/__pycache__/__init__.cpython-310.pyc,,
watchdog/observers/__pycache__/api.cpython-310.pyc,,
watchdog/observers/__pycache__/fsevents.cpython-310.pyc,,
watchdog/observers/__pycache__/fsevents2.cpython-310.pyc,,
watchdog/observers/__pycache__/inotify.cpython-310.pyc,,
watchdog/observers/__pycache__/inotify_buffer.cpython-310.pyc,,
watchdog/observers/__pycache__/inotify_c.cpython-310.pyc,,
watchdog/observers/__pycache__/kqueue.cpython-310.pyc,,
watchdog/observers/__pycache__/polling.cpython-310.pyc,,
watchdog/observers/__pycache__/read_directory_changes.cpython-310.pyc,,
watchdog/observers/__pycache__/winapi.cpython-310.pyc,,
watchdog/observers/api.py,sha256=tyPhqDhzh2pveEkCVRR2B51SaXlDsrcpl8W6oidQuUs,13802
watchdog/observers/fsevents.py,sha256=6ho3sgBQt6hsq_1F6eJgtor5CjZV09TaoT01xeHNgd4,14284
watchdog/observers/fsevents2.py,sha256=fZ8O7zomltBgvucDBlUHZIWKREFn4MKB30T5iEisPsc,9439
watchdog/observers/inotify.py,sha256=i075APHIwIUdiFrfdocpbYsedUvh_u3AtLqU3bqlgFE,10649
watchdog/observers/inotify_buffer.py,sha256=VWWuleFQRaJL_ZM0jbcAGjGdj1kZUWmUigC4WSbPmyQ,4434
watchdog/observers/inotify_c.py,sha256=ys6NrVEbw2bPZQUkzshY5lGlfDZT4nz58Vmn4A49emU,21129
watchdog/observers/kqueue.py,sha256=HYARzdF_Nlu72vTaIBbIvh5e_N7ROd33VDtZc1Ab2oc,24425
watchdog/observers/polling.py,sha256=B8gyEtzkRiHmSSf8dcY_0P-Bu0U5i-VWQanBqIVxTs0,4932
watchdog/observers/read_directory_changes.py,sha256=mKHZIfUopCAU0ae7oE9Ttkggk7LfTb8lwgcMJHmOW5E,4167
watchdog/observers/winapi.py,sha256=q2nTZ2JYnbuEUiByTcSmpbw4iiKhtNQ6UQg9CWPIUxY,11635
watchdog/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
watchdog/tricks/__init__.py,sha256=zLFpZfXN_A3jXlH00Ok0SpbtWxP2WwwsxG7w2PXkxP8,9492
watchdog/tricks/__pycache__/__init__.cpython-310.pyc,,
watchdog/utils/__init__.py,sha256=9q7wEZW7yl81Db1CcLBQJ33xZ2YrGwyzsK2gzNCC-lE,3513
watchdog/utils/__pycache__/__init__.cpython-310.pyc,,
watchdog/utils/__pycache__/bricks.cpython-310.pyc,,
watchdog/utils/__pycache__/delayed_queue.cpython-310.pyc,,
watchdog/utils/__pycache__/dirsnapshot.cpython-310.pyc,,
watchdog/utils/__pycache__/echo.cpython-310.pyc,,
watchdog/utils/__pycache__/event_debouncer.cpython-310.pyc,,
watchdog/utils/__pycache__/patterns.cpython-310.pyc,,
watchdog/utils/__pycache__/platform.cpython-310.pyc,,
watchdog/utils/__pycache__/process_watcher.cpython-310.pyc,,
watchdog/utils/bricks.py,sha256=cZCA4T9e1iWvr3yY4FOGuOzIpdP4c4Nhx0ipfgyhBno,2545
watchdog/utils/delayed_queue.py,sha256=Xv-Rco10b62Vt4LnLEmZxlpr93MeBV2ZX-U2--nHS5A,2615
watchdog/utils/dirsnapshot.py,sha256=SaGnxq0miZbxLZcpJcSOj5sE8ihunqjb5-m8ww9F1aA,14740
watchdog/utils/echo.py,sha256=GALgUot9zXVAMxMwLN5CkHRIdjmio1X3vppVS14P-eM,2210
watchdog/utils/event_debouncer.py,sha256=oVifdt50PjOiV4n-0W1OZOCNO4j689BRRVgCGrJvH0E,2079
watchdog/utils/patterns.py,sha256=fjy8h_XaUuRkoh46uFLv37JzgjmmK2Vzt1ZVbMNa0cE,3672
watchdog/utils/platform.py,sha256=VSN45Y2kA0NS1Nzlmr5SHB4Ct0xqK2KRly9jlx7DHSg,885
watchdog/utils/process_watcher.py,sha256=qgLtEZLhYLjpL-GZawIEjV-p_PnMka_hBGH_1vxX-gk,925
watchdog/version.py,sha256=guvQM7983CuZ2S52zEtdhsS_dkqhbY3mp-GQvVM4DR0,349
watchdog/watchmedo.py,sha256=AMTUpNSlQmAqxf5EhtfKKh5eYiPZoDYgUm1Gf8xD3-g,25452
