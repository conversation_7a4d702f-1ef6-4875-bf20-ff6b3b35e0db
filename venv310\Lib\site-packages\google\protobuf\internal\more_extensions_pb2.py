# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/internal/more_extensions.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.google/protobuf/internal/more_extensions.proto\x12\x18google.protobuf.internal\"\x99\x01\n\x0fTopLevelMessage\x12\x41\n\nsubmessage\x18\x01 \x01(\x0b\x32).google.protobuf.internal.ExtendedMessageB\x02(\x01\x12\x43\n\x0enested_message\x18\x02 \x01(\x0b\x32\'.google.protobuf.internal.NestedMessageB\x02(\x01\"R\n\rNestedMessage\x12\x41\n\nsubmessage\x18\x01 \x01(\x0b\x32).google.protobuf.internal.ExtendedMessageB\x02(\x01\"K\n\x0f\x45xtendedMessage\x12\x17\n\x0eoptional_int32\x18\xe9\x07 \x01(\x05\x12\x18\n\x0frepeated_string\x18\xea\x07 \x03(\t*\x05\x08\x01\x10\xe8\x07\"-\n\x0e\x46oreignMessage\x12\x1b\n\x13\x66oreign_message_int\x18\x01 \x01(\x05:I\n\x16optional_int_extension\x12).google.protobuf.internal.ExtendedMessage\x18\x01 \x01(\x05:w\n\x1aoptional_message_extension\x12).google.protobuf.internal.ExtendedMessage\x18\x02 \x01(\x0b\x32(.google.protobuf.internal.ForeignMessage:I\n\x16repeated_int_extension\x12).google.protobuf.internal.ExtendedMessage\x18\x03 \x03(\x05:w\n\x1arepeated_message_extension\x12).google.protobuf.internal.ExtendedMessage\x18\x04 \x03(\x0b\x32(.google.protobuf.internal.ForeignMessage')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'google.protobuf.internal.more_extensions_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:
  ExtendedMessage.RegisterExtension(optional_int_extension)
  ExtendedMessage.RegisterExtension(optional_message_extension)
  ExtendedMessage.RegisterExtension(repeated_int_extension)
  ExtendedMessage.RegisterExtension(repeated_message_extension)

  DESCRIPTOR._options = None
  _TOPLEVELMESSAGE.fields_by_name['submessage']._options = None
  _TOPLEVELMESSAGE.fields_by_name['submessage']._serialized_options = b'(\001'
  _TOPLEVELMESSAGE.fields_by_name['nested_message']._options = None
  _TOPLEVELMESSAGE.fields_by_name['nested_message']._serialized_options = b'(\001'
  _NESTEDMESSAGE.fields_by_name['submessage']._options = None
  _NESTEDMESSAGE.fields_by_name['submessage']._serialized_options = b'(\001'
  _TOPLEVELMESSAGE._serialized_start=77
  _TOPLEVELMESSAGE._serialized_end=230
  _NESTEDMESSAGE._serialized_start=232
  _NESTEDMESSAGE._serialized_end=314
  _EXTENDEDMESSAGE._serialized_start=316
  _EXTENDEDMESSAGE._serialized_end=391
  _FOREIGNMESSAGE._serialized_start=393
  _FOREIGNMESSAGE._serialized_end=438
# @@protoc_insertion_point(module_scope)
