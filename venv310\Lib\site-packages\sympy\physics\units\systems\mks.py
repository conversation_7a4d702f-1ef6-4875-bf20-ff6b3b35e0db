"""
MKS unit system.

MKS stands for "meter, kilogram, second".
"""

from sympy.physics.units import UnitSystem
from sympy.physics.units.definitions import gravitational_constant, hertz, joule, newton, pascal, watt, speed_of_light, gram, kilogram, meter, second
from sympy.physics.units.definitions.dimension_definitions import (
    acceleration, action, energy, force, frequency, momentum,
    power, pressure, velocity, length, mass, time)
from sympy.physics.units.prefixes import PREFIXES, prefix_unit
from sympy.physics.units.systems.length_weight_time import dimsys_length_weight_time

dims = (velocity, acceleration, momentum, force, energy, power, pressure,
        frequency, action)

units = [meter, gram, second, joule, newton, watt, pascal, hertz]
all_units = []

# Prefixes of units like gram, joule, newton etc get added using `prefix_unit`
# in the for loop, but the actual units have to be added manually.
all_units.extend([gram, joule, newton, watt, pascal, hertz])

for u in units:
    all_units.extend(prefix_unit(u, PREFIXES))
all_units.extend([gravitational_constant, speed_of_light])

# unit system
MKS = UnitSystem(base_units=(meter, kilogram, second), units=all_units, name="MKS", dimension_system=dimsys_length_weight_time, derived_units={
    power: watt,
    time: second,
    pressure: pascal,
    length: meter,
    frequency: hertz,
    mass: kilogram,
    force: newton,
    energy: joule,
    velocity: meter/second,
    acceleration: meter/(second**2),
})


__all__ = [
    'MKS', 'units', 'all_units', 'dims',
]
